# S3 File Upload Implementation

This document explains the S3 file upload implementation that has been added to the Yellow Penguin Laravel application.

## Overview

The application has been updated to use Amazon S3 for file storage instead of local storage. This provides better scalability, reliability, and performance for file uploads.

## What Was Implemented

### 1. AWS SDK Installation
- Installed `league/flysystem-aws-s3-v3` package for S3 integration
- AWS credentials are already configured in your `.env` file

### 2. S3FileUploadService
**Location:** `app/Services/S3FileUploadService.php`

This is the core service that handles all S3 operations:
- `uploadFile()` - Upload a single file to S3
- `uploadImage()` - Upload images with custom naming
- `uploadMultipleFiles()` - Upload multiple files at once
- `deleteFile()` - Delete a file from S3
- `deleteMultipleFiles()` - Delete multiple files
- `fileExists()` - Check if a file exists
- `getFileUrl()` - Get the public URL of a file
- `validateFileType()` - Validate file types
- `validateFileSize()` - Validate file sizes
- `formatFileSize()` - Format file sizes for display

### 3. FileUploadHelper
**Location:** `app/Services/FileUploadHelper.php`

A helper service that simplifies file upload operations:
- `processFileUploads()` - Process multiple files from a request
- `processSingleFile()` - Process a single file
- `deleteFiles()` - Delete files from S3
- `getFileUrl()` - Get file URLs
- `validateFile()` - Validate files before upload

### 4. Updated Components

#### HelperTrait
- Updated `imageUpload()` method to use S3
- Added `getImageUrl()` method to retrieve S3 URLs

#### Request Classes Updated:
- `SupportRequestStoreRequest`
- `SupportRequestUpdateRequest`
- `ContractDraftStoreRequest`

#### Services Updated:
- `ProfileService` - Avatar uploads now use S3

#### Controllers Updated:
- `ContractController` - File uploads in status updates use S3

## How to Use

### Basic File Upload

```php
use App\Services\S3FileUploadService;

$s3Service = new S3FileUploadService();
$result = $s3Service->uploadFile($uploadedFile, 'directory-name');

if ($result) {
    // File uploaded successfully
    $filePath = $result['path'];        // S3 path for database storage
    $fileUrl = $result['url'];          // Public URL for display
    $originalName = $result['original_name'];
    $fileType = $result['file_type'];
    $fileSize = $result['file_size'];
}
```

### Using FileUploadHelper

```php
use App\Services\FileUploadHelper;

$helper = new FileUploadHelper();
$files = $helper->processFileUploads($request, 'files', 'upload-directory');

foreach ($files as $file) {
    // Save file info to database
    Model::create([
        'file_path' => $file['path'],
        'original_name' => $file['original_name'],
        'file_type' => $file['type'],
        'file_size' => $file['size'],
    ]);
}
```

### Image Upload with HelperTrait

```php
// In your controller that uses HelperTrait
$imagePath = $this->imageUpload($request, 'image', 'avatars', $oldImagePath);

// To get the full URL for display
$imageUrl = $this->getImageUrl($imagePath);
```

## File Storage Structure

Files are stored in S3 with the following structure:
```
yellowpenguin/
├── avatars/
├── support_requests/
├── contract_drafts/
├── contract_status/
├── contract_acceptance/
└── admin_contract_completion/
```

## Configuration

Your S3 configuration is already set up in:
- **Bucket:** yellowpenguin
- **Region:** ap-southeast-1
- **Access Key:** AKIAZMRWUMQZPYLQOUWF
- **Secret Key:** [Configured in .env]

## File URLs

Files uploaded to S3 will have URLs in this format:
```
https://yellowpenguin.s3.ap-southeast-1.amazonaws.com/directory/filename.ext
```

## Database Storage

When storing file information in the database:
- Store the S3 **path** (e.g., `avatars/filename.jpg`) in the database
- Use the S3FileUploadService to get the full URL when needed for display

## Error Handling

All S3 operations include proper error handling:
- Failed uploads return `null`
- Errors are logged to Laravel's log system
- Graceful fallbacks for missing files

## File Validation

The services include built-in validation for:
- File size limits
- File type restrictions
- File validity checks

Example validation:
```php
$helper = new FileUploadHelper();
$isValid = $helper->validateFile(
    $file, 
    ['image/jpeg', 'image/png', 'application/pdf'], // Allowed types
    10 // Max size in MB
);
```

## Migration Notes

- **Old local files:** Existing local files will continue to work
- **New uploads:** All new uploads will go to S3
- **URLs:** Update your views to use the new URL generation methods
- **File deletion:** Use the S3 service methods for deleting files

## Testing

A connection test script is available at `test_s3_connection.php` to verify S3 connectivity.

## Benefits

1. **Scalability:** No local storage limitations
2. **Reliability:** AWS S3 provides 99.999999999% durability
3. **Performance:** Global CDN capabilities
4. **Security:** Proper access controls and encryption
5. **Cost-effective:** Pay only for what you use

## Next Steps

1. Update any remaining file upload code to use the new S3 services
2. Consider implementing image resizing/optimization
3. Set up S3 lifecycle policies for cost optimization
4. Implement file backup strategies if needed
