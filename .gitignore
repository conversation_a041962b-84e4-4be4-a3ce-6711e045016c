# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
vendor/

# Ignore all files in public directory except specific ones
public/*
!public/.htaccess
!public/favicon.ico
!public/index.php
!public/robots.txt
!public/web.config

# Preserve upload directory structure but ignore uploaded files
!public/uploads/
public/uploads/*
!public/uploads/.gitkeep
!public/uploads/contract_status/
public/uploads/contract_status/*
!public/uploads/contract_status/.gitkeep
!public/uploads/contract_acceptance/
public/uploads/contract_acceptance/*
!public/uploads/contract_acceptance/.gitkeep
!public/uploads/rfp_files/
public/uploads/rfp_files/*
!public/uploads/rfp_files/.gitkeep
!public/uploads/rfp_images/
public/uploads/rfp_images/*
!public/uploads/rfp_images/.gitkeep

# Explicitly ignore all uploaded files with common extensions
public/uploads/**/*.pdf
public/uploads/**/*.doc
public/uploads/**/*.docx
public/uploads/**/*.jpg
public/uploads/**/*.jpeg
public/uploads/**/*.png
public/uploads/**/*.gif
public/uploads/**/*.svg
public/uploads/**/*.zip
# env
.env

# Log files
*.log

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

