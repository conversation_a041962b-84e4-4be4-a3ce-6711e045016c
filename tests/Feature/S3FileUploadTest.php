<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Services\S3FileUploadService;
use App\Services\FileUploadHelper;

class S3FileUploadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Use fake S3 disk for testing
        Storage::fake('s3');
    }

    /** @test */
    public function it_can_upload_a_file_to_s3()
    {
        $s3Service = new S3FileUploadService();
        
        // Create a fake file
        $file = UploadedFile::fake()->image('test-image.jpg', 100, 100);
        
        // Upload the file
        $result = $s3Service->uploadFile($file, 'test-directory');
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertArrayHasKey('path', $result);
        $this->assertArrayHasKey('url', $result);
        $this->assertArrayHasKey('original_name', $result);
        $this->assertArrayHasKey('file_type', $result);
        $this->assertArrayHasKey('file_size', $result);
        
        // Assert file exists in S3
        Storage::disk('s3')->assertExists($result['path']);
    }

    /** @test */
    public function it_can_upload_an_image_with_custom_filename()
    {
        $s3Service = new S3FileUploadService();
        
        // Create a fake image
        $image = UploadedFile::fake()->image('avatar.png', 200, 200);
        
        // Upload with custom filename
        $result = $s3Service->uploadImage($image, 'avatars');
        
        // Assert the result
        $this->assertNotNull($result);
        $this->assertStringContains('yellow_penguin_', $result['filename']);
        $this->assertStringContains('avatars/', $result['path']);
        
        // Assert file exists in S3
        Storage::disk('s3')->assertExists($result['path']);
    }

    /** @test */
    public function it_can_delete_a_file_from_s3()
    {
        $s3Service = new S3FileUploadService();
        
        // Create and upload a fake file
        $file = UploadedFile::fake()->create('document.pdf', 100);
        $result = $s3Service->uploadFile($file, 'documents');
        
        // Verify file exists
        Storage::disk('s3')->assertExists($result['path']);
        
        // Delete the file
        $deleted = $s3Service->deleteFile($result['path']);
        
        // Assert deletion was successful
        $this->assertTrue($deleted);
        Storage::disk('s3')->assertMissing($result['path']);
    }

    /** @test */
    public function it_can_upload_multiple_files()
    {
        $s3Service = new S3FileUploadService();
        
        // Create multiple fake files
        $files = [
            UploadedFile::fake()->image('image1.jpg', 100, 100),
            UploadedFile::fake()->create('document.pdf', 100),
            UploadedFile::fake()->image('image2.png', 150, 150),
        ];
        
        // Upload multiple files
        $results = $s3Service->uploadMultipleFiles($files, 'mixed-files');
        
        // Assert all files were uploaded
        $this->assertCount(3, $results);
        
        foreach ($results as $result) {
            $this->assertArrayHasKey('path', $result);
            Storage::disk('s3')->assertExists($result['path']);
        }
    }

    /** @test */
    public function file_upload_helper_processes_request_files()
    {
        // Create a mock request with files
        $files = [
            UploadedFile::fake()->image('test1.jpg', 100, 100),
            UploadedFile::fake()->create('test2.pdf', 100),
        ];
        
        // Create a mock request
        $request = new \Illuminate\Http\Request();
        $request->files->set('files', $files);
        
        $helper = new FileUploadHelper();
        $results = $helper->processFileUploads($request, 'files', 'test-uploads');
        
        // Assert results
        $this->assertCount(2, $results);
        
        foreach ($results as $result) {
            $this->assertArrayHasKey('path', $result);
            $this->assertArrayHasKey('url', $result);
            $this->assertArrayHasKey('original_name', $result);
            $this->assertArrayHasKey('type', $result);
            $this->assertArrayHasKey('size', $result);
            
            Storage::disk('s3')->assertExists($result['path']);
        }
    }

    /** @test */
    public function it_validates_file_types_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        // Test valid image file
        $imageFile = UploadedFile::fake()->image('test.jpg');
        $this->assertTrue($s3Service->validateFileType($imageFile, ['image/jpeg', 'jpg']));
        
        // Test invalid file type
        $textFile = UploadedFile::fake()->create('test.txt', 100, 'text/plain');
        $this->assertFalse($s3Service->validateFileType($textFile, ['image/jpeg', 'jpg']));
    }

    /** @test */
    public function it_validates_file_size_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        // Test file within size limit (1MB file, 2MB limit)
        $smallFile = UploadedFile::fake()->create('small.pdf', 1024); // 1MB
        $this->assertTrue($s3Service->validateFileSize($smallFile, 2));
        
        // Test file exceeding size limit (3MB file, 2MB limit)
        $largeFile = UploadedFile::fake()->create('large.pdf', 3072); // 3MB
        $this->assertFalse($s3Service->validateFileSize($largeFile, 2));
    }

    /** @test */
    public function it_handles_file_upload_errors_gracefully()
    {
        $s3Service = new S3FileUploadService();
        
        // Create an invalid file
        $invalidFile = new UploadedFile(
            '/nonexistent/path',
            'test.jpg',
            'image/jpeg',
            UPLOAD_ERR_NO_FILE
        );
        
        // Attempt to upload invalid file
        $result = $s3Service->uploadFile($invalidFile, 'test');
        
        // Should return null for invalid file
        $this->assertNull($result);
    }

    /** @test */
    public function it_formats_file_size_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        $this->assertEquals('1 KB', $s3Service->formatFileSize(1024));
        $this->assertEquals('1 MB', $s3Service->formatFileSize(1024 * 1024));
        $this->assertEquals('1.5 MB', $s3Service->formatFileSize(1024 * 1024 * 1.5));
    }
}
