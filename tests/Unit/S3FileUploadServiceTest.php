<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Services\S3FileUploadService;

class S3FileUploadServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the Storage facade
        Storage::fake('s3');
    }

    public function test_it_formats_file_size_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        $this->assertEquals('1 KB', $s3Service->formatFileSize(1024));
        $this->assertEquals('1 MB', $s3Service->formatFileSize(1024 * 1024));
        $this->assertEquals('1.5 MB', $s3Service->formatFileSize(1024 * 1024 * 1.5));
        $this->assertEquals('2 GB', $s3Service->formatFileSize(1024 * 1024 * 1024 * 2));
    }

    public function test_it_validates_file_size_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        // Create a mock file that's 1MB (1024 * 1024 bytes)
        $smallFile = $this->createMockFile('small.pdf', 1024 * 1024);
        $this->assertTrue($s3Service->validateFileSize($smallFile, 2)); // 2MB limit
        
        // Create a mock file that's 3MB
        $largeFile = $this->createMockFile('large.pdf', 3 * 1024 * 1024);
        $this->assertFalse($s3Service->validateFileSize($largeFile, 2)); // 2MB limit
    }

    public function test_it_validates_file_type_correctly()
    {
        $s3Service = new S3FileUploadService();
        
        // Test JPEG file
        $jpegFile = $this->createMockFile('test.jpg', 1024, 'image/jpeg');
        $this->assertTrue($s3Service->validateFileType($jpegFile, ['image/jpeg', 'jpg']));
        $this->assertFalse($s3Service->validateFileType($jpegFile, ['application/pdf', 'pdf']));
        
        // Test PDF file
        $pdfFile = $this->createMockFile('test.pdf', 1024, 'application/pdf');
        $this->assertTrue($s3Service->validateFileType($pdfFile, ['application/pdf', 'pdf']));
        $this->assertFalse($s3Service->validateFileType($pdfFile, ['image/jpeg', 'jpg']));
    }

    /**
     * Create a mock UploadedFile for testing
     */
    private function createMockFile(string $name, int $size, string $mimeType = 'application/octet-stream'): UploadedFile
    {
        $mock = $this->createMock(UploadedFile::class);
        
        $mock->method('getClientOriginalName')->willReturn($name);
        $mock->method('getSize')->willReturn($size);
        $mock->method('getClientMimeType')->willReturn($mimeType);
        $mock->method('getClientOriginalExtension')->willReturn(pathinfo($name, PATHINFO_EXTENSION));
        $mock->method('isValid')->willReturn(true);
        
        return $mock;
    }
}
