<?php

namespace Database\Seeders;

use App\Models\ContractType;
use Illuminate\Database\Seeder;

class ContractTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contractTypes = [
            [
                'title' => 'HomePage',
                'short_description' => 'Basic homepage website with essential features',
                'construction_cost' => 330000,
                'subscription_fee' => 55000,
                'category' => 'homepage',
                'slug' => 'homepage',
                'is_active' => true,
                'sort_order' => 1,
                'features' => [
                    'Responsive design',
                    'Basic SEO optimization',
                    'Contact form',
                    'Social media integration',
                    'Mobile-friendly layout'
                ],
                'icon' => 'fas fa-home',
                'color' => '#3B82F6',
                'detailed_description' => 'A professional homepage website perfect for businesses looking to establish their online presence. Includes all essential features needed for a modern website.',
            ],
            [
                'title' => 'E-commerce',
                'short_description' => 'Full-featured online store with payment integration',
                'construction_cost' => 1100000,
                'subscription_fee' => 110000,
                'category' => 'ecommerce',
                'slug' => 'ecommerce',
                'is_active' => true,
                'sort_order' => 2,
                'features' => [
                    'Product catalog management',
                    'Shopping cart functionality',
                    'Payment gateway integration',
                    'Order management system',
                    'Inventory tracking',
                    'Customer accounts',
                    'Admin dashboard',
                    'Mobile commerce ready'
                ],
                'icon' => 'fas fa-shopping-cart',
                'color' => '#10B981',
                'detailed_description' => 'A comprehensive e-commerce solution with all the features needed to run a successful online store. Includes payment processing, inventory management, and customer management.',
            ],
            [
                'title' => 'ERP System',
                'short_description' => 'Enterprise Resource Planning system for business management',
                'construction_cost' => 0, // Will be decided when creating contracts
                'subscription_fee' => 0, // Will be decided when creating contracts
                'category' => 'erp',
                'slug' => 'erp-system',
                'is_active' => true,
                'sort_order' => 3,
                'features' => [
                    'Resource planning',
                    'Financial management',
                    'Human resources',
                    'Supply chain management',
                    'Customer relationship management',
                    'Reporting and analytics',
                    'Multi-user access control',
                    'Custom workflows'
                ],
                'icon' => 'fas fa-cogs',
                'color' => '#8B5CF6',
                'detailed_description' => 'A powerful ERP system designed to streamline business operations. Pricing will be customized based on specific requirements and scope.',
            ],
            [
                'title' => 'SI Domain',
                'short_description' => 'System Integration domain solutions',
                'construction_cost' => 0, // Will be decided when creating contracts
                'subscription_fee' => 0, // Will be decided when creating contracts
                'category' => 'si',
                'slug' => 'si-domain',
                'is_active' => true,
                'sort_order' => 4,
                'features' => [
                    'System integration',
                    'API development',
                    'Data migration',
                    'Third-party integrations',
                    'Custom solutions',
                    'Technical consulting'
                ],
                'icon' => 'fas fa-network-wired',
                'color' => '#F59E0B',
                'detailed_description' => 'Specialized system integration solutions tailored to your business needs. Pricing will be determined based on project complexity and requirements.',
            ],
            [
                'title' => 'Start Up Package',
                'short_description' => 'Complete startup solution package',
                'construction_cost' => 0, // Will be decided when creating contracts
                'subscription_fee' => 0, // Will be decided when creating contracts
                'category' => 'startup',
                'slug' => 'startup-package',
                'is_active' => true,
                'sort_order' => 5,
                'features' => [
                    'MVP development',
                    'Scalable architecture',
                    'Cloud deployment',
                    'Basic analytics',
                    'User authentication',
                    'Admin panel',
                    'Technical support',
                    'Growth planning'
                ],
                'icon' => 'fas fa-rocket',
                'color' => '#EF4444',
                'detailed_description' => 'A comprehensive package designed for startups looking to launch their digital product. Includes everything needed to get started with room for growth.',
            ],
        ];

        foreach ($contractTypes as $contractType) {
            ContractType::create($contractType);
        }
    }
}
