<?php

namespace Database\Seeders;

use App\Models\User;

use App\Models\Role;
use App\Models\Permission;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Call development items seeder
        $this->call(DevelopmentItemSeeder::class);

        // Call contract types seeder
        $this->call(ContractTypeSeeder::class);

        // Create users
        // $testUser = User::create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        //     'phone_no' => '01672164422',
        //     'username' => 'testuser',
        //     'password' => bcrypt('Password@123'),
        //     'email_verified_at' => now(),
        // ]);

        // $adminUser = User::create([
        //     'name' => 'Admin User',
        //     'email' => '<EMAIL>',
        //     'phone_no' => '01234567890',
        //     'username' => 'admin',
        //     'password' => bcrypt('Password@123'),
        //     'email_verified_at' => now(),
        // ]);

        // $bacbonUser = User::create([
        //     'name' => 'Bacbon User',
        //     'email' => '<EMAIL>',
        //     'phone_no' => '01987654321',
        //     'username' => 'bacbon',
        //     'password' => bcrypt('Password@123'),
        //     'email_verified_at' => now(),
        // ]);


        // $adminRole = Role::create(['name' => 'admin']);
        // $bacbonRole = Role::create(['name' => 'bacbon']);
        // $visitorRole = Role::create(['name' => 'visitor']);

        // $viewReportsPermission = Permission::create(['name' => 'view-reports']);
        // $editArticlesPermission = Permission::create(['name' => 'edit-articles']);
        // $createUsersPermission = Permission::create(['name' => 'create-users']);

        // $adminRole->permissions()->attach([$viewReportsPermission->id, $editArticlesPermission->id, $createUsersPermission->id]);
        // $bacbonRole->permissions()->attach([$viewReportsPermission->id, $editArticlesPermission->id, $createUsersPermission->id]);
        // $visitorRole->permissions()->attach([$editArticlesPermission->id]);

        // // Assign roles to users
        // $adminUser->roles()->attach($adminRole);
        // $bacbonUser->roles()->attach($bacbonRole);
        // $testUser->roles()->attach($visitorRole);
    }
}
