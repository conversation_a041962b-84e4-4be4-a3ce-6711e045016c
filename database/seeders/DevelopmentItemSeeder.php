<?php

namespace Database\Seeders;

use App\Models\DevelopmentItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DevelopmentItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create development items based on the provided examples
        $items = [
            // Pages
            [
                'name' => 'Additional Page',
                'description' => 'Cost for each additional page in the website',
                'price' => 100000,
                'unit' => 'per page',
                'category' => 'pages',
                'is_active' => true,
            ],

            // Languages
            [
                'name' => 'Multilingual Support',
                'description' => 'Support for additional languages',
                'price' => 50000,
                'unit' => 'per language',
                'category' => 'languages',
                'is_active' => true,
            ],

            // Forms
            [
                'name' => 'Inquiry Form',
                'description' => 'Support for inquiry forms',
                'price' => 50000,
                'unit' => 'per form',
                'category' => 'forms',
                'is_active' => true,
            ],

            // Boards
            [
                'name' => 'Bulletin Board',
                'description' => 'Support for bulletin boards',
                'price' => 100000,
                'unit' => 'per board',
                'category' => 'boards',
                'is_active' => true,
            ],

            // Popups
            [
                'name' => 'Pop-up Window',
                'description' => 'Support for pop-up windows',
                'price' => 50000,
                'unit' => 'per popup',
                'category' => 'popups',
                'is_active' => true,
            ],

            // Branding
            [
                'name' => 'CI Support',
                'description' => 'Corporate Identity support',
                'price' => 200000,
                'unit' => 'each',
                'category' => 'branding',
                'is_active' => true,
            ],
            [
                'name' => 'BI Support',
                'description' => 'Brand Identity support',
                'price' => 200000,
                'unit' => 'each',
                'category' => 'branding',
                'is_active' => true,
            ],

            // Email
            [
                'name' => 'Email Support',
                'description' => 'Email support for 3 IDs',
                'price' => 150000,
                'unit' => 'for 3 IDs',
                'category' => 'email',
                'is_active' => true,
            ],

            // Payment
            [
                'name' => 'Payment System',
                'description' => 'Integration of payment system',
                'price' => 100000,
                'unit' => 'each',
                'category' => 'payment',
                'is_active' => true,
            ],
        ];

        foreach ($items as $item) {
            DevelopmentItem::create($item);
        }
    }
}
