<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            $table->unsignedBigInteger('contract_type_id')->nullable()->after('parent_id');
            
            // Add foreign key constraint
            $table->foreign('contract_type_id')
                  ->references('id')
                  ->on('penguin_contract_types')
                  ->onDelete('set null');
                  
            // Add index for better performance
            $table->index('contract_type_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['contract_type_id']);
            
            // Drop index
            $table->dropIndex(['contract_type_id']);
            
            // Drop column
            $table->dropColumn('contract_type_id');
        });
    }
};
