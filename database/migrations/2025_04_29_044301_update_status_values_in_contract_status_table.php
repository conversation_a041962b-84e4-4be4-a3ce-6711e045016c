<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penguin_contract_statuses', function (Blueprint $table) {
            $table->enum('status', ['Pending','New Lead', 'Accepted', 'Approved', 'Development', 'Maintenance', 'Developed', 'Completed', 'Rejected'])->default('New Lead')
            ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penguin_contract_statuses', function (Blueprint $table) {
            //
        });
    }
};
