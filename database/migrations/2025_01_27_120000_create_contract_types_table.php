<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_contract_types', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('short_description')->nullable();
            $table->decimal('construction_cost', 12, 2);
            $table->decimal('subscription_fee', 12, 2);
            $table->string('category')->nullable(); // e.g., 'homepage', 'ecommerce', 'erp', etc.
            $table->string('slug')->unique(); // URL-friendly version of title
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0); // For ordering display
            $table->json('features')->nullable(); // JSON array of features/benefits
            $table->string('icon')->nullable(); // Icon class or image path
            $table->string('color')->nullable(); // Theme color for UI
            $table->text('detailed_description')->nullable(); // Longer description
            $table->softDeletes();
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_contract_types');
    }
};
