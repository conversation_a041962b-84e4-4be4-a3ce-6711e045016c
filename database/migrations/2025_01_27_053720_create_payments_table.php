<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contact_id')
                ->constrained('penguin_contracts')
                ->onDelete('cascade');

            $table->decimal('agreed_payment', 10, 2);
            $table->decimal('paid_amount', 10, 2)->nullable();
            $table->enum('status', ['pending', 'partial', 'complete']);
            $table->date('payment_date')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Adding indexes for frequently queried columns for performance optimization
            $table->index('contact_id');
            $table->index('status');
            $table->index('payment_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes before dropping the table
        Schema::table('penguin_payments', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['payment_date']);
        });

        Schema::dropIfExists('penguin_payments');
    }
};

