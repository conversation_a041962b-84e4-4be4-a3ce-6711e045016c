<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('unique_id');
            $table->string('contract_name');
            $table->text('contract_details')->nullable();
            $table->string('page_url')->nullable();
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->enum('status', ['New Lead', 'Accepted', 'Approved', 'Development', 'Maintenance', 'Completed', 'Rejected'])->default('New Lead');
            $table->decimal('amount', 10, 2)->nullable();
            $table->unsignedBigInteger('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_contracts');
    }
};
