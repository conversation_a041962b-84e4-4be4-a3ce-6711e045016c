<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            $table->decimal('base_construction_cost', 12, 2)->nullable()->after('amount');
            $table->decimal('monthly_subscription_fee', 12, 2)->nullable()->after('base_construction_cost');
            $table->decimal('additional_items_total', 12, 2)->nullable()->after('monthly_subscription_fee');
            $table->decimal('total_construction_cost', 12, 2)->nullable()->after('additional_items_total');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            $table->dropColumn([
                'base_construction_cost',
                'monthly_subscription_fee',
                'additional_items_total',
                'total_construction_cost'
            ]);
        });
    }
};
