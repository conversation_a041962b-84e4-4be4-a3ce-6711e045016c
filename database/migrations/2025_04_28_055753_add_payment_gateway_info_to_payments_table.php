<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penguin_payments', function (Blueprint $table) {
            $table->foreignId('payment_gateway_id')->nullable()->after('payment_type')->constrained('penguin_payment_gateways')->nullOnDelete();
            $table->foreignId('payment_gateway_method_id')->nullable()->after('payment_gateway_id')->constrained('penguin_payment_gateway_methods')->nullOnDelete();
            $table->string('payment_gateway_name')->nullable()->after('payment_gateway_method_id');
            $table->string('payment_gateway_method_name')->nullable()->after('payment_gateway_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penguin_payments', function (Blueprint $table) {
            $table->dropForeign(['payment_gateway_id']);
            $table->dropForeign(['payment_gateway_method_id']);
            $table->dropColumn([
                'payment_gateway_id',
                'payment_gateway_method_id',
                'payment_gateway_name',
                'payment_gateway_method_name'
            ]);
        });
    }
};
