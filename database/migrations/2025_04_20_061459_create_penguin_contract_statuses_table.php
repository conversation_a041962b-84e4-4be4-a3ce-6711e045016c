<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_contract_statuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_id')->constrained('penguin_contracts')->onDelete('cascade');
            $table->enum('status', ['New Lead', 'Accepted', 'Approved', 'Development', 'Maintenance', 'Completed', 'Rejected'])->default('New Lead');
            $table->string('comment')->nullable();
            $table->string('file')->nullable();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_contract_statuses');
    }
};
