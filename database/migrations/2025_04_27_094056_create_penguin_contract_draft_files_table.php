<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_contract_draft_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('draft_id')->constrained('penguin_contract_drafts')->onDelete('cascade');
            $table->enum('type', ['file', 'url'])->default('file');
            $table->string('file_path')->nullable();
            $table->string('url')->nullable();
            $table->string('original_filename')->nullable();
            $table->string('file_type')->nullable();
            $table->integer('file_size')->nullable(); // in bytes
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_contract_draft_files');
    }
};
