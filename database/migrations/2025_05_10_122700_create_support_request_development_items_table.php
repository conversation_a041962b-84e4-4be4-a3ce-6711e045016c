<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_support_request_development_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('support_request_id');
            $table->unsignedBigInteger('development_item_id')->nullable();
            $table->string('name');
            $table->decimal('price', 12, 2);
            $table->integer('quantity');
            $table->decimal('total', 12, 2);
            $table->softDeletes();
            $table->timestamps();

            // Add foreign keys with custom names to avoid length issues
            $table->foreign('support_request_id', 'sr_dev_items_sr_id_foreign')
                ->references('id')
                ->on('penguin_support_requests')
                ->onDelete('cascade');

            $table->foreign('development_item_id', 'sr_dev_items_dev_id_foreign')
                ->references('id')
                ->on('penguin_development_items')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_support_request_development_items');
    }
};
