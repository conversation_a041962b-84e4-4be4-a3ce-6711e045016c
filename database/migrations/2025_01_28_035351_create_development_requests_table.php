<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_development_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contact_id')->constrained('penguin_contracts')->onDelete('cascade');
            $table->string('request_title');
            $table->text('request_details')->nullable();
            $table->enum('status', ['Pending', 'Rejected', 'Complete'])->default('Pending');
            $table->text('remarks')->nullable();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_development_requests');
    }
};

