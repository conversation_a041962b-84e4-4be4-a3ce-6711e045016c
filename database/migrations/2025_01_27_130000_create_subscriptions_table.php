<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_id')->constrained('penguin_contracts')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->string('subscription_code')->unique(); // SUB-YYMMDD-XXX
            $table->decimal('monthly_amount', 12, 2); // Monthly subscription fee
            $table->date('start_date'); // When subscription starts
            $table->date('end_date')->nullable(); // When subscription ends (null for ongoing)
            $table->enum('status', ['active', 'paused', 'cancelled', 'expired'])->default('active');
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly'])->default('monthly');
            $table->integer('billing_day')->default(1); // Day of month to bill (1-28)
            $table->date('next_billing_date'); // Next payment due date
            $table->date('last_billing_date')->nullable(); // Last successful payment date
            $table->boolean('auto_renewal')->default(true); // Auto-renew subscription
            $table->integer('grace_period_days')->default(7); // Days after due date before suspension
            $table->text('notes')->nullable(); // Admin notes
            $table->json('metadata')->nullable(); // Additional data (payment methods, etc.)
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['customer_id', 'status']);
            $table->index(['status', 'next_billing_date']);
            $table->index('subscription_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_subscriptions');
    }
};
