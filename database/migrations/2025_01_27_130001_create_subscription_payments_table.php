<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penguin_subscription_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained('penguin_subscriptions')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->string('payment_code')->unique(); // PAY-YYMMDD-XXX
            $table->decimal('amount', 12, 2); // Payment amount
            $table->date('due_date'); // When payment is due
            $table->date('paid_date')->nullable(); // When payment was made
            $table->enum('status', ['pending', 'paid', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('payment_method', ['credit_card', 'bank_transfer', 'paypal', 'stripe', 'manual', 'other'])->nullable();
            $table->string('payment_gateway')->nullable(); // Gateway used (stripe, paypal, etc.)
            $table->string('transaction_id')->nullable(); // Gateway transaction ID
            $table->string('invoice_number')->nullable(); // Invoice reference
            $table->date('billing_period_start'); // Start of billing period
            $table->date('billing_period_end'); // End of billing period
            $table->text('payment_notes')->nullable(); // Payment-specific notes
            $table->json('payment_details')->nullable(); // Gateway response, receipt info, etc.
            $table->decimal('late_fee', 8, 2)->default(0); // Late payment fee
            $table->integer('retry_count')->default(0); // Number of payment retry attempts
            $table->timestamp('next_retry_at')->nullable(); // Next retry attempt
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['subscription_id', 'status']);
            $table->index(['customer_id', 'status']);
            $table->index(['status', 'due_date']);
            $table->index('payment_code');
            $table->index('transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penguin_subscription_payments');
    }
};
