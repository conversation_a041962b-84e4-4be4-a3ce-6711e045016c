<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify status enum to include 'Pending'
        DB::statement("ALTER TABLE penguin_contracts MODIFY COLUMN status ENUM('New Lead', 'Pending', 'Accepted', 'Approved', 'Development', 'Maintenance', 'Completed', 'Rejected') DEFAULT 'New Lead'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert status enum to original values
        DB::statement("ALTER TABLE penguin_contracts MODIFY COLUMN status ENUM('New Lead', 'Accepted', 'Approved', 'Development', 'Maintenance', 'Completed', 'Rejected') DEFAULT 'New Lead'");
    }
};
