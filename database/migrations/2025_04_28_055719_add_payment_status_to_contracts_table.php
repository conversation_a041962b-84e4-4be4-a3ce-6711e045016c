<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            $table->enum('payment_status', ['Unpaid', 'Partial', 'Paid'])->default('Unpaid')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penguin_contracts', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });
    }
};
