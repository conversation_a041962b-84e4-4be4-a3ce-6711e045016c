<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\DevelopmentItemController;
use App\Http\Controllers\DevelopmentRequestController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PaymentGatewayMethodController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SupportRequestController;
use App\Http\Controllers\ContractDraftController;
use App\Http\Controllers\ContractTypeController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SubscriptionPaymentController;

// Public routes (no authentication required)
Route::post('/log-me-in', [AuthController::class, 'logMeIn']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);
Route::post('/forgot-username', [AuthController::class, 'forgotUserName']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/verify-otp', [AuthController::class, 'verifyOtp']);
Route::post('/verify-secret', [AuthController::class, 'verifySecret']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// OAuth routes
Route::get('/auth/kakao/redirect', [AuthController::class, 'kakaoLogin']);
Route::get('/auth/kakao/callback', [AuthController::class, 'kakaoCallback']);
Route::post('/auth/google', [AuthController::class, 'googleLogin']);
Route::post('/auth/google/callback', [AuthController::class, 'googleCallback']);

// Support request routes - accessible to both authenticated and guest users
Route::post('/support-requests', [SupportRequestController::class, 'store']);
Route::post('/support-requests/find-by-email', [SupportRequestController::class, 'findByEmail']);

// Test route for debugging support request creation
Route::get('/test-support-request-form', function() {
    return view('test-support-request-form');
});

Route::get('check-username', [UserController::class, 'checkUsername']);

// Public contract type routes (for pricing display)
// Route::get('contract-types', [ContractTypeController::class, 'getActive']);
Route::get('contract-types/category/{category}', [ContractTypeController::class, 'getByCategory']);

Route::get('active-contract-types', [ContractTypeController::class, 'getActive']);


// Routes that require authentication (any role)
Route::middleware('auth:sanctum')->group(function () {
    // Common routes for all authenticated users
    Route::get('/profile', [ProfileController::class, 'show']);
    Route::put('/profile', [ProfileController::class, 'update']);
    Route::post('/profile/update-avatar', [ProfileController::class, 'updateAvatar']);
    Route::put('/profile/update-password', [ProfileController::class, 'updatePassword']);
    Route::get('/profile/statistics', [ProfileController::class, 'statistics']);
    Route::get('/logout', [AuthController::class, 'logout']);

    // Support request routes
    Route::get('/support-requests', [SupportRequestController::class, 'index']);
    Route::get('/support-requests/{id}', [SupportRequestController::class, 'show']);
    Route::put('/support-requests/{id}', [SupportRequestController::class, 'update']);
    Route::delete('/support-requests/{id}', [SupportRequestController::class, 'destroy']);
    Route::delete('/support-request-files/{id}', [SupportRequestController::class, 'deleteFile']);

    // Contract draft routes for all authenticated users
    Route::get('/contract-drafts/{id}', [ContractDraftController::class, 'show']);
    Route::get('/contracts/{contractId}/drafts', [ContractDraftController::class, 'getDraftsByContract']);
});

// Permission-based routes
Route::middleware(['auth:sanctum', 'permission:view-reports'])->get('/reports', function () {
    return 'Reports Page';
});

// Admin-only routes
Route::middleware(['auth:sanctum', 'role:admin'])->group(function () {
    // Admin page
    Route::get('/admin', function () {
        return 'Admin Page';
    });

    // Admin support request routes
    Route::get('/admin/support-requests', [SupportRequestController::class, 'index']);
    Route::get('/admin/support-requests/{id}', [SupportRequestController::class, 'show']);
    Route::put('/admin/support-requests/{id}', [SupportRequestController::class, 'update']);
    Route::delete('/admin/support-requests/{id}', [SupportRequestController::class, 'destroy']);
    Route::delete('/admin/support-request-files/{id}', [SupportRequestController::class, 'deleteFile']);

    Route::post('/admin/support-requests-convert-to-contracts', [SupportRequestController::class, 'convertToContracts']);
    // Admin contract routes
    Route::post('/contracts/{id}/approve', [ContractController::class, 'approveContract']);
    Route::post('/contracts/{id}/send-to-development', [ContractController::class, 'sendToDevelopment']);
    Route::post('/contracts/{id}/complete', [ContractController::class, 'adminCompleteContract']);

    // Admin user management routes
    Route::get('users', [UserController::class, 'index']);
    Route::get('search-users', [UserController::class, 'searchUser']);
    Route::get('users/{user}', [UserController::class, 'show']);
    Route::put('users/{user}', [UserController::class, 'update']);
    Route::delete('users/{user}', [UserController::class, 'destroy']);
    Route::post('users', [UserController::class, 'store']);



    // Payment gateway method routes
    Route::get('payment-gateways/{paymentGatewayId}/methods', [PaymentGatewayMethodController::class, 'getByPaymentGatewayId']);
    Route::apiResource('payment-gateway-methods', PaymentGatewayMethodController::class);

    // Development item routes
    Route::apiResource('development-items', DevelopmentItemController::class);
    Route::get('development-items/category/{category}', [DevelopmentItemController::class, 'getByCategory']);

    // Contract type routes (admin only)
    Route::apiResource('contract-types', ContractTypeController::class);
    Route::get('contract-types/category/{category}', [ContractTypeController::class, 'getByCategory']);
    // Subscription routes (admin only)
    Route::apiResource('subscriptions', SubscriptionController::class);
    Route::post('subscriptions/{id}/cancel', [SubscriptionController::class, 'cancel']);
    Route::get('subscriptions/{id}/payments', [SubscriptionController::class, 'getPayments']);

    // Subscription payment routes (admin only)
    Route::get('subscription-payments', [SubscriptionPaymentController::class, 'index']);
    Route::get('subscription-payments/{id}', [SubscriptionPaymentController::class, 'show']);
    Route::post('subscription-payments/{id}/process', [SubscriptionPaymentController::class, 'processPayment']);
    Route::post('subscription-payments/{id}/mark-failed', [SubscriptionPaymentController::class, 'markAsFailed']);
    Route::get('overdue-payments', [SubscriptionPaymentController::class, 'getOverduePayments']);
    Route::get('payments-due-today', [SubscriptionPaymentController::class, 'getPaymentsDueToday']);
});

// Visitor-only routes
Route::middleware(['auth:sanctum', 'role:visitor'])->group(function () {
    // Visitor contract routes
    Route::post('/contracts/{id}/accept', [ContractController::class, 'acceptContract']);
    Route::post('/visitor-contract-requests', [ContractController::class, 'createVisitorRequest']);
    Route::post('/visitor-contracts', [ContractController::class, 'createVisitorContract']);

    // Visitor contract draft routes
    Route::post('/contract-drafts/{id}/respond', [ContractDraftController::class, 'respondToDraft']);

    // Visitor subscription routes (own subscriptions only)
    Route::get('my-subscriptions', [SubscriptionController::class, 'index']);
    Route::get('my-subscriptions/{id}', [SubscriptionController::class, 'show']);
    Route::post('my-subscriptions/{id}/cancel', [SubscriptionController::class, 'cancel']);
    Route::get('my-subscriptions/{id}/payments', [SubscriptionController::class, 'getPayments']);

    // Visitor payment routes (own payments only)
    Route::get('my-payments', [SubscriptionPaymentController::class, 'index']);
    Route::get('my-payments/{id}', [SubscriptionPaymentController::class, 'show']);
    Route::post('my-payments/{id}/process', [SubscriptionPaymentController::class, 'processPayment']);
});

// Bacbon-only routes
Route::middleware(['auth:sanctum', 'role:bacbon'])->group(function () {
    // Bacbon contract routes
    Route::get('/bacbon/contracts', [ContractController::class, 'getBackbonContracts']);

    Route::post('/contracts/{id}/in-progress', [ContractController::class, 'inProgressContract']);

    Route::post('/contracts/{id}/make-done', [ContractController::class, 'makeDoneContract']);
    // Bacbon contract draft routes
    Route::post('/contract-drafts', [ContractDraftController::class, 'store']);
    Route::delete('/contract-draft-files/{id}', [ContractDraftController::class, 'deleteFile']);

});

// Routes accessible by multiple roles but requiring authentication
Route::middleware('auth:sanctum')->group(function () {
    // Contract routes (accessible by all roles)

    // Payment gateway routes
    Route::apiResource('payment-gateways', PaymentGatewayController::class);
    Route::apiResource('contracts', ContractController::class);
    Route::post('make-new-request', [ContractController::class, 'makeNewRequest']);
    Route::delete('/delete-rfp-file/{id}', [ContractController::class, 'deleteRfpFile']);
    Route::delete('/delete-rfp-image/{id}', [ContractController::class, 'deleteRfpImage']);
    Route::post('/contracts/{id}/add-status-remark', [ContractController::class, 'addStatusRemark']);

    // Payment routes
    Route::apiResource('payments', PaymentController::class);

    // Contract payment routes (admin only)
    Route::get('contract-payments', [PaymentController::class, 'getContractPayments']);
    Route::get('contract-payments/{id}', [PaymentController::class, 'getContractPaymentDetails']);
    Route::post('contract-payments/{id}/process', [PaymentController::class, 'processContractPayment']);
    Route::post('contract-payments/{id}/complete', [PaymentController::class, 'completeContractPayment']);
    Route::post('contract-payments/{id}/negotiate', [PaymentController::class, 'negotiateContractPayment']);

    // Development request routes
    Route::apiResource('development-requests', DevelopmentRequestController::class);

    // Development items routes (read-only for non-admin users)
    Route::get('development-items', [DevelopmentItemController::class, 'index']);
    Route::get('development-items/{id}', [DevelopmentItemController::class, 'show']);
    Route::get('development-items/category/{category}', [DevelopmentItemController::class, 'getByCategory']);
});
