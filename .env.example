#APP_NAME=Laravel
APP_NAME="Yellow Penguin"
APP_ENV=local
APP_KEY=base64:7IbIdWXBJ553eljCJHYBSqlz6xiklLZdCesBdwua/Wc=
APP_DEBUG=true
APP_URL=https://api.yellowpenguin.co.kr

APP_URL_SWAGGER=https://api.yellowpenguin.co.kr

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

#DB_CONNECTION=mysql
#DB_HOST=*************
#DB_PORT=3306
#DB_DATABASE=yellow_penguine_db
#DB_USERNAME=BaCbOn
#DB_PASSWORD="BacB0n*7^23#"


DB_CONNECTION=mysql
DB_HOST=**************
DB_PORT=3306
DB_DATABASE=yellow_penguine_db
DB_USERNAME=root
DB_PASSWORD="B*()cyTBD%^>"


BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_APP_ENV="${APP_ENV}"
VITE_APP_URL="${APP_URL}"
VITE_APP_DEBUG=true


MAIL_MAILER=smtp
MAIL_HOST=smtp.zoho.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="!G6cxtcr"
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

KAKAO_CLIENT_ID=55ad8b084326e0ba67eb18bb054c9e7d
KAKAO_CLIENT_SECRET=lOsIGvOLUcCu376uuW0cvoLHUhI2vOo4
KAKAO_REDIRECT_URI=https://api.yellowpenguin.co.kr/api/auth/kakao/callback

# Production
GOOGLE_CLIENT_ID=743083705791-hkcn29gb6v5vo9effhrdgdio9h6332m9.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://api.yellowpenguin.co.kr/api/auth/google/callback