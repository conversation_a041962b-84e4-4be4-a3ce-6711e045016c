<?php

// Test script to simulate the exact payload for support request creation

// Define the API endpoint
$url = 'http://localhost:8000/api/support-requests';

// Define the payload
$payload = [
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'request_type' => 'homepage',
    'title' => 'Generate a Homepage',
    'description' => '<p>To know about details read the documents please </p>',
    'base_construction_cost' => 330000,
    'monthly_subscription_fee' => 55000,
    'additional_items_total' => 750000,
    'total_construction_cost' => 1080000,
    'development_items' => json_encode([
        [
            'id' => 1,
            'name' => 'Additional Page',
            'quantity' => 5,
            'price' => '100000.00',
            'total' => 500000
        ],
        [
            'id' => 4,
            'name' => 'Bulletin Board',
            'quantity' => 1,
            'price' => '100000.00',
            'total' => 100000
        ],
        [
            'id' => 8,
            'name' => 'Email Support',
            'quantity' => 1,
            'price' => '150000.00',
            'total' => 150000
        ]
    ])
];

// Create a temporary file for testing
$tempFile1 = tempnam(sys_get_temp_dir(), 'test_file_1');
file_put_contents($tempFile1, 'Test file 1 content');

$tempFile2 = tempnam(sys_get_temp_dir(), 'test_file_2');
file_put_contents($tempFile2, 'Test file 2 content');

$tempFile3 = tempnam(sys_get_temp_dir(), 'test_file_3');
file_put_contents($tempFile3, 'Test file 3 content');

// Initialize cURL
$ch = curl_init();

// Create a CURLFile object for each file
$cfile1 = curl_file_create($tempFile1, 'text/plain', 'test_file_1.txt');
$cfile2 = curl_file_create($tempFile2, 'text/plain', 'test_file_2.txt');
$cfile3 = curl_file_create($tempFile3, 'text/plain', 'test_file_3.txt');

// Add files to the payload
$payload['files[0]'] = $cfile1;
$payload['files[1]'] = $cfile2;
$payload['files[2]'] = $cfile3;

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Execute cURL request
$response = curl_exec($ch);

// Check for errors
if (curl_errno($ch)) {
    echo 'Error: ' . curl_error($ch);
} else {
    // Print the response
    echo "Response:\n";
    echo $response;
}

// Close cURL
curl_close($ch);

// Clean up temporary files
unlink($tempFile1);
unlink($tempFile2);
unlink($tempFile3);

echo "\nTest completed.\n";
