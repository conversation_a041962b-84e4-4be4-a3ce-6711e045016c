<?php

namespace App\Trait;

use App\Services\S3FileUploadService;

trait HelperTrait
{
    
    protected function apiResponse($data = null, $message = null, $message_kr = null, $status = null, $statusCode = null)
    {
        $array = [
            'status' => $status,
            'message' => $message,
            'message_kr' => $message_kr,
            'data' => $data,
        ];

        return response()->json($array, $statusCode);
    }

    protected function generateOtp($length = 6)
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= mt_rand(0, 9);
        }
        return $otp;
    }



    protected function generateSecretKey ($length = 6)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $secretKey = '';
        for ($i = 0; $i < $length; $i++) {
            $secretKey .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $secretKey;
    }


    protected function imageUpload($request, $imageField, $destination, $oldImage = null)
    {
        $imageUrl = null;
        if ($request->hasFile($imageField)) {
            $s3Service = new S3FileUploadService();

            // Upload image to S3
            $image = $request->file($imageField);
            $result = $s3Service->uploadImage($image, $destination, $oldImage);

            if ($result) {
                $imageUrl = $result['path']; // Return S3 path for database storage
            }
        }

        return $imageUrl;
    }

    /**
     * Get the full URL for an image stored in S3
     *
     * @param string|null $imagePath
     * @return string|null
     */
    protected function getImageUrl($imagePath)
    {
        if (!$imagePath) {
            return null;
        }

        $s3Service = new S3FileUploadService();
        return $s3Service->getFileUrl($imagePath);
    }

}
