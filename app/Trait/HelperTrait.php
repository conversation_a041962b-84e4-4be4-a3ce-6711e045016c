<?php

namespace App\Trait;

trait HelperTrait
{
    
    protected function apiResponse($data = null, $message = null, $message_kr = null, $status = null, $statusCode = null)
    {
        $array = [
            'status' => $status,
            'message' => $message,
            'message_kr' => $message_kr,
            'data' => $data,
        ];

        return response()->json($array, $statusCode);
    }

    protected function generateOtp($length = 6)
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= mt_rand(0, 9);
        }
        return $otp;
    }



    protected function generateSecretKey ($length = 6)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $secretKey = '';
        for ($i = 0; $i < $length; $i++) {
            $secretKey .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $secretKey;
    }


    protected function imageUpload($request, $imageField, $destination, $oldImage = null)
    {
        $imageUrl = null;
        if ($request->hasFile($imageField)) {
            // Remove old image if it exists
            if ($oldImage) {
                $oldImagePath = public_path('uploads/'.$oldImage);
                if (file_exists($oldImagePath)) {
                    @unlink($oldImagePath); // Use @ to suppress errors if the file doesn't exist
                }
            }
            // Process new image upload
            $image = $request->file($imageField);
            $imageName = 'yellow_penguin'.time().'.'.$image->getClientOriginalExtension();
            $imageDestination = public_path('uploads/'.$destination);
            $image->move($imageDestination, $imageName);

            $imageUrl = $destination.'/'.$imageName;
        }

        return $imageUrl;
    }

}
