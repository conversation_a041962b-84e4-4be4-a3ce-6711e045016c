<?php

namespace App\Console\Commands;

use App\Services\SubscriptionService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ProcessSubscriptionBilling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:process-billing {--date= : Process billing for specific date (Y-m-d format)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process subscription billing for active subscriptions';

    /**
     * Execute the console command.
     */
    public function handle(SubscriptionService $subscriptionService)
    {
        $date = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::today();
        
        $this->info("Processing subscription billing for date: {$date->toDateString()}");
        
        $results = $subscriptionService->processBilling($date);
        
        $this->info("Billing processing completed:");
        $this->info("- Processed: {$results['processed']} subscriptions");
        $this->info("- Failed: {$results['failed']} subscriptions");
        
        if (!empty($results['errors'])) {
            $this->error("Errors encountered:");
            foreach ($results['errors'] as $error) {
                $this->error("- Subscription ID {$error['subscription_id']}: {$error['error']}");
            }
        }
        
        return Command::SUCCESS;
    }
}
