<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\Kakao\KakaoExtendSocialite;
use SocialiteProviders\Naver\NaverExtendSocialite;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->app->singleton(SocialiteWasCalled::class, function () {
            return new SocialiteWasCalled();
        });

        // Register Kakao and Naver providers
        (new KakaoExtendSocialite())->handle(app(SocialiteWasCalled::class));
        (new NaverExtendSocialite())->handle(app(SocialiteWasCalled::class));
    }
}
