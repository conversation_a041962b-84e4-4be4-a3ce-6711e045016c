<?php

namespace App\Services;

use App\Models\Contract;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ProfileService
{
    /**
     * Get the authenticated user's profile
     *
     * @return User
     */
    public function getProfile()
    {
        return Auth::user()->load('roles');
    }

    /**
     * Update the authenticated user's profile
     *
     * @param array $data
     * @return User
     */
    public function updateProfile(array $data)
    {
        $user = Auth::user();
        $user->update($data);
        return $user->fresh()->load('roles');
    }

    /**
     * Update the authenticated user's avatar
     *
     * @param \Illuminate\Http\UploadedFile $avatar
     * @return User
     */
    public function updateAvatar($avatar)
    {
        $user = Auth::user();
        
        // Remove old avatar if it exists
        if ($user->avatar) {
            $oldAvatarPath = public_path('uploads/' . $user->avatar);
            if (file_exists($oldAvatarPath)) {
                @unlink($oldAvatarPath);
            }
        }
        
        // Upload new avatar
        $imageName = 'avatar_' . $user->id . '_' . time() . '.' . $avatar->getClientOriginalExtension();
        $destination = 'avatars';
        $avatar->move(public_path('uploads/' . $destination), $imageName);
        
        // Update user record
        $user->avatar = $destination . '/' . $imageName;
        $user->save();
        
        return $user->fresh()->load('roles');
    }

    /**
     * Update the authenticated user's password
     *
     * @param string $password
     * @return User
     */
    public function updatePassword(string $password)
    {
        $user = Auth::user();
        $user->password = Hash::make($password);
        $user->save();
        
        return $user->fresh()->load('roles');
    }

    /**
     * Get statistics for the authenticated user based on their role
     *
     * @return array
     */
    public function getStatistics()
    {
        $user = Auth::user();
        $statistics = [];
        
        if ($user->hasRole('visitor')) {
            // For visitor: contract count and total payment
            $contractCount = Contract::where('customer_id', $user->id)->count();
            $totalPayment = Payment::whereHas('contract', function ($query) use ($user) {
                $query->where('customer_id', $user->id);
            })->sum('paid_amount');
            
            $statistics = [
                'contract_count' => $contractCount,
                'total_payment' => $totalPayment,
            ];
        } elseif ($user->hasRole('bacbon')) {
            // For bacbon: total assigned, completed, and running contracts
            $totalAssigned = Contract::where('created_by', $user->id)->count();
            $completedContracts = Contract::where('created_by', $user->id)
                ->where('status', 'completed')
                ->count();
            $runningContracts = Contract::where('created_by', $user->id)
                ->where('status', 'running')
                ->count();
            
            $statistics = [
                'total_assigned' => $totalAssigned,
                'completed_contracts' => $completedContracts,
                'running_contracts' => $runningContracts,
            ];
        }
        
        return $statistics;
    }
}
