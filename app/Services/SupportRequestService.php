<?php

namespace App\Services;

use App\Models\SupportRequest;
use App\Models\SupportRequestFile;
use App\Models\SupportRequestDevelopmentItem;
use App\Models\User;
use App\Notifications\NewSupportRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class SupportRequestService
{
    /**
     * Store a new support request.
     *
     * @param array $data
     * @return SupportRequest
     */
    public function store(array $data, array $developmentItems)
    {
        // Create the support request
        $supportRequest = SupportRequest::create([
            'request_type' => $data['request_type'],
            'title' => $data['title'],
            'description' => $data['description'],
            'base_construction_cost' => $data['base_construction_cost'] ?? null,
            'monthly_subscription_fee' => $data['monthly_subscription_fee'] ?? null,
            'additional_items_total' => $data['additional_items_total'] ?? null,
            'total_construction_cost' => $data['total_construction_cost'] ?? null,
            'name' => $data['name'] ?? null,
            'email' => $data['email'] ?? null,
            'user_id' => $data['user_id'] ?? null,
            'status' => 'New',
        ]);

        // Process files if any
        if (!empty($data['files'])) {
            foreach ($data['files'] as $fileData) {
                SupportRequestFile::create([
                    'support_request_id' => $supportRequest->id,
                    'file_path' => $fileData['path'],
                    'original_filename' => $fileData['original_name'],
                    'file_type' => $fileData['type'],
                    'file_size' => $fileData['size'],
                ]);
            }
        }

        // Process development items if any
        if (!empty($developmentItems)) {
            foreach ($developmentItems as $itemData) {
                try {
                    SupportRequestDevelopmentItem::create([
                        'support_request_id' => $supportRequest->id,
                        'development_item_id' => $itemData['id'],
                        'name' => $itemData['name'],
                        'price' => $itemData['price'],
                        'quantity' => $itemData['quantity'],
                        'total' => $itemData['total'],
                    ]);
                } catch (\Exception $e) {
                    // Log error but continue with other items
                    \Log::error('Error creating development item: ' . $e->getMessage(), [
                        'item_data' => $itemData,
                        'exception' => $e
                    ]);
                }
            }
        }

        // Notify admin users
        $this->notifyAdmins($supportRequest);

        return $supportRequest->load('files', 'developmentItems');
    }

    /**
     * Get all support requests.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function all()
    {
        // If user is admin, return all requests
        if (Auth::check() && Auth::user()->hasRole('admin')) {
            return SupportRequest::with('files', 'user', 'developmentItems')->latest()->get();
        }

        // If user is authenticated but not admin, return only their requests
        if (Auth::check()) {
            return SupportRequest::with(['files', 'user', 'developmentItems'])
                ->where('user_id', Auth::id())
                ->latest()
                ->get();
        }

        // Guest users shouldn't be able to see any requests
        return collect();
    }

    /**
     * Get a specific support request.
     *
     * @param int $id
     * @return SupportRequest|null
     */
    public function find($id)
    {
        $query = SupportRequest::with(['files', 'developmentItems']);

        // If user is not admin, restrict to their own requests
        if (Auth::check() && !Auth::user()->hasRole('admin')) {
            $query->where(function ($q) {
                $q->where('user_id', Auth::id())
                ->orWhere('email', Auth::user()->email);
            });
        }

        $supportRequest = $query->find($id);

        if (!$supportRequest) {
            return null;
        }

        // Try to associate user if not already associated
        if (!$supportRequest->user_id && $supportRequest->email) {
            $user = User::where('email', $supportRequest->email)->first();
            if ($user) {
                $supportRequest->user_id = $user->id;
                $supportRequest->save();
            }
        }

        // Lazy-load the user relation if needed
        $supportRequest->load('user');

        return $supportRequest;
    }

    /**
     * Update a support request.
     *
     * @param SupportRequest $supportRequest
     * @param array $data
     * @return SupportRequest
     */
    public function update(SupportRequest $supportRequest, array $data): SupportRequest
    {
        // Update the support request
        $supportRequest->update([
            'request_type' => $data['request_type'] ?? $supportRequest->request_type,
            'title' => $data['title'] ?? $supportRequest->title,
            'description' => $data['description'] ?? $supportRequest->description,
            'status' => $data['status'] ?? $supportRequest->status,
        ]);

        // Process files if any
        if (!empty($data['files'])) {
            foreach ($data['files'] as $fileData) {
                SupportRequestFile::create([
                    'support_request_id' => $supportRequest->id,
                    'file_path' => $fileData['path'],
                    'original_filename' => $fileData['original_name'],
                    'file_type' => $fileData['type'],
                    'file_size' => $fileData['size'],
                ]);
            }
        }

        return $supportRequest->fresh(['files', 'developmentItems']);
    }

    /**
     * Delete a support request.
     *
     * @param SupportRequest $supportRequest
     * @return bool
     */
    public function delete(SupportRequest $supportRequest): bool
    {
        // Delete all associated files first
        foreach ($supportRequest->files as $file) {
            $this->deleteFile($file);
        }

        // Delete all associated development items
        foreach ($supportRequest->developmentItems as $item) {
            $item->delete();
        }

        // Delete the support request
        return $supportRequest->delete();
    }

    /**
     * Delete a specific file from a support request.
     *
     * @param SupportRequestFile $file
     * @return bool
     */
    public function deleteFile(SupportRequestFile $file): bool
    {
        // Delete the physical file if it exists
        $filePath = public_path('uploads/' . $file->file_path);
        if (file_exists($filePath)) {
            @unlink($filePath);
        }

        // Delete the file record
        return $file->delete();
    }

    /**
     * Find support requests by email.
     *
     * @param string $email
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findByEmail(string $email)
    {
        return SupportRequest::with(['files', 'developmentItems'])
            ->where('email', $email)
            ->latest()
            ->get();
    }

    /**
     * Check if user can access a support request.
     *
     * @param SupportRequest $supportRequest
     * @return bool
     */
    public function canAccess(SupportRequest $supportRequest): bool
    {
        // Admin can access any request
        if (Auth::check() && Auth::user()->hasRole('admin')) {
            return true;
        }

        // Authenticated user can access their own requests
        if (Auth::check() && $supportRequest->user_id === Auth::id()) {
            return true;
        }

        // Guest user with matching email can access their requests
        if (!Auth::check() && request()->has('email') && $supportRequest->email === request()->email) {
            return true;
        }

        return false;
    }

    /**
     * Notify admin users about the new support request.
     *
     * @param SupportRequest $supportRequest
     * @return void
     */
    private function notifyAdmins(SupportRequest $supportRequest): void
    {
        // Get all admin users
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        // Send notification to all admins
        Notification::send($admins, new NewSupportRequest($supportRequest));
    }
}
