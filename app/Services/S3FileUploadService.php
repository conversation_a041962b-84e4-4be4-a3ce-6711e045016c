<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class S3FileUploadService
{
    /**
     * Upload a single file to S3
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string|null $customFilename
     * @return array|null Returns array with file info or null on failure
     */
    public function uploadFile(UploadedFile $file, string $directory, ?string $customFilename = null): ?array
    {
        try {
            // Validate file
            if (!$file->isValid()) {
                Log::error('Invalid file upload attempt', [
                    'original_name' => $file->getClientOriginalName(),
                    'error' => $file->getErrorMessage()
                ]);
                return null;
            }

            // Generate filename
            $filename = $customFilename ?: $this->generateFilename($file);
            $path = $directory . '/' . $filename;

            // Upload to S3
            $uploaded = Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()), 'public');

            if (!$uploaded) {
                Log::error('Failed to upload file to S3', ['path' => $path]);
                return null;
            }

            // Get the S3 URL
            $url = Storage::disk('s3')->url($path);

            return [
                'path' => $path,
                'url' => $url,
                'original_name' => $file->getClientOriginalName(),
                'file_type' => $file->getClientMimeType(),
                'file_size' => $file->getSize(),
                'filename' => $filename
            ];

        } catch (\Exception $e) {
            Log::error('S3 file upload error', [
                'message' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'directory' => $directory
            ]);
            return null;
        }
    }

    /**
     * Upload multiple files to S3
     *
     * @param array $files Array of UploadedFile objects
     * @param string $directory
     * @return array Array of uploaded file info
     */
    public function uploadMultipleFiles(array $files, string $directory): array
    {
        $uploadedFiles = [];

        foreach ($files as $file) {
            $result = $this->uploadFile($file, $directory);
            if ($result) {
                $uploadedFiles[] = $result;
            }
        }

        return $uploadedFiles;
    }

    /**
     * Upload an image file with optional resizing
     *
     * @param UploadedFile $image
     * @param string $directory
     * @param string|null $oldImagePath Path to old image to delete
     * @return array|null
     */
    public function uploadImage(UploadedFile $image, string $directory, ?string $oldImagePath = null): ?array
    {
        // Delete old image if provided
        if ($oldImagePath) {
            $this->deleteFile($oldImagePath);
        }

        // Generate image filename with prefix
        $filename = 'yellow_penguin_' . time() . '.' . $image->getClientOriginalExtension();
        
        return $this->uploadFile($image, $directory, $filename);
    }

    /**
     * Delete a file from S3
     *
     * @param string $path
     * @return bool
     */
    public function deleteFile(string $path): bool
    {
        try {
            if (Storage::disk('s3')->exists($path)) {
                return Storage::disk('s3')->delete($path);
            }
            return true; // File doesn't exist, consider it deleted
        } catch (\Exception $e) {
            Log::error('S3 file deletion error', [
                'message' => $e->getMessage(),
                'path' => $path
            ]);
            return false;
        }
    }

    /**
     * Delete multiple files from S3
     *
     * @param array $paths
     * @return bool
     */
    public function deleteMultipleFiles(array $paths): bool
    {
        try {
            $existingPaths = array_filter($paths, function($path) {
                return Storage::disk('s3')->exists($path);
            });

            if (empty($existingPaths)) {
                return true;
            }

            return Storage::disk('s3')->delete($existingPaths);
        } catch (\Exception $e) {
            Log::error('S3 multiple files deletion error', [
                'message' => $e->getMessage(),
                'paths' => $paths
            ]);
            return false;
        }
    }

    /**
     * Check if a file exists in S3
     *
     * @param string $path
     * @return bool
     */
    public function fileExists(string $path): bool
    {
        try {
            return Storage::disk('s3')->exists($path);
        } catch (\Exception $e) {
            Log::error('S3 file existence check error', [
                'message' => $e->getMessage(),
                'path' => $path
            ]);
            return false;
        }
    }

    /**
     * Get file URL from S3
     *
     * @param string $path
     * @return string|null
     */
    public function getFileUrl(string $path): ?string
    {
        try {
            if ($this->fileExists($path)) {
                return Storage::disk('s3')->url($path);
            }
            return null;
        } catch (\Exception $e) {
            Log::error('S3 get file URL error', [
                'message' => $e->getMessage(),
                'path' => $path
            ]);
            return null;
        }
    }

    /**
     * Generate a unique filename
     *
     * @param UploadedFile $file
     * @return string
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $sanitizedName = Str::slug($originalName);
        
        return time() . '_' . $sanitizedName . '.' . $extension;
    }

    /**
     * Get file size in human readable format
     *
     * @param int $bytes
     * @return string
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Validate file type against allowed types
     *
     * @param UploadedFile $file
     * @param array $allowedTypes
     * @return bool
     */
    public function validateFileType(UploadedFile $file, array $allowedTypes): bool
    {
        $mimeType = $file->getClientMimeType();
        $extension = strtolower($file->getClientOriginalExtension());
        
        return in_array($mimeType, $allowedTypes) || in_array($extension, $allowedTypes);
    }

    /**
     * Validate file size
     *
     * @param UploadedFile $file
     * @param int $maxSizeInMB
     * @return bool
     */
    public function validateFileSize(UploadedFile $file, int $maxSizeInMB): bool
    {
        $maxSizeInBytes = $maxSizeInMB * 1024 * 1024;
        return $file->getSize() <= $maxSizeInBytes;
    }
}
