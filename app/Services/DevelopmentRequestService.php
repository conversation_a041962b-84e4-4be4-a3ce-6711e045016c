<?php

namespace App\Services;

use App\Models\DevelopmentRequest;
use Illuminate\Support\Facades\Auth;

class DevelopmentRequestService
{
    private DevelopmentRequest $model;

    public function __construct(DevelopmentRequest $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        $user = Auth::user();
        $query = $this->model->orderBy('id', 'desc');

        if (!$user->hasRole('admin')) {
            $query->where('user_id', $user->id);
        }

        if (request()->query('pagination')) {
            return $query->paginate(
                request()->query('per_page', 10),
                ['*'],
                'page',
                request()->query('current_page', 1)
            );
        }

        return $query->get();
    }


    public function findByContractId($contractId)
    {
        return $this->model->where('contract_id', $contractId)->orderBy('id', 'desc')->get();
    }

    public function find($id)
    {
        return $this->model->find($id);
    }

    public function store($data)
    {
        return $this->model->create($data);
    }

    public function storeMultiple($data)
    {
        return $this->model->insert($data);
    }

    public function update(DevelopmentRequest $developmentRequest, $data)
    {
        $developmentRequest->update($data);
        return $developmentRequest; // Corrected the variable returned here.
    }
    public function delete($id)
    {
        try {
            $developmentRequest = $this->find($id);
            if (!$developmentRequest) {
                throw new \Exception('Development request not found.', 404);
            }
            $developmentRequest->delete();
        } catch (\Exception $e) {
            throw new \Exception('Error deleting development request.', 500);
        }
    }
}
