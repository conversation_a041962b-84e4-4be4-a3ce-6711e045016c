<?php

namespace App\Services;

use App\Models\Contract;
use App\Models\Subscription;
use App\Models\SubscriptionPayment;
use Carbon\Carbon;

class SubscriptionService
{
    /**
     * Create subscription from contract
     */
    public function createFromContract(Contract $contract, array $options = []): Subscription
    {
        // Determine monthly amount from contract
        $monthlyAmount = $this->determineMonthlyAmount($contract, $options);
        
        // Set subscription start date
        $startDate = isset($options['start_date']) 
            ? Carbon::parse($options['start_date'])
            : ($contract->start_date ? Carbon::parse($contract->start_date) : Carbon::now());
        
        // Set billing day (default to start date day, but ensure it's between 1-28)
        $billingDay = min($startDate->day, 28);
        
        // Calculate next billing date
        $nextBillingDate = $startDate->copy()->day($billingDay);
        if ($nextBillingDate->isPast()) {
            $nextBillingDate->addMonth();
        }
        
        // Create subscription
        $subscription = Subscription::create([
            'contract_id' => $contract->id,
            'customer_id' => $contract->customer_id,
            'monthly_amount' => $monthlyAmount,
            'start_date' => $startDate,
            'end_date' => $contract->end_date ? Carbon::parse($contract->end_date) : null,
            'status' => 'active',
            'billing_cycle' => $options['billing_cycle'] ?? 'monthly',
            'billing_day' => $billingDay,
            'next_billing_date' => $nextBillingDate,
            'auto_renewal' => $options['auto_renewal'] ?? true,
            'grace_period_days' => $options['grace_period_days'] ?? 7,
            'notes' => $options['notes'] ?? "Subscription created from contract {$contract->unique_id}",
            'metadata' => $options['metadata'] ?? [],
        ]);
        
        // Create first payment record
        $this->createNextPayment($subscription);
        
        return $subscription;
    }
    
    /**
     * Determine monthly amount from contract
     */
    private function determineMonthlyAmount(Contract $contract, array $options): float
    {
        // Priority order:
        // 1. Explicitly provided monthly amount
        // 2. Contract's monthly subscription fee
        // 3. Contract type's subscription fee
        // 4. Default to 0
        
        if (isset($options['monthly_amount'])) {
            return (float) $options['monthly_amount'];
        }
        
        if ($contract->monthly_subscription_fee) {
            return (float) $contract->monthly_subscription_fee;
        }
        
        if ($contract->contractType && $contract->contractType->subscription_fee) {
            return (float) $contract->contractType->subscription_fee;
        }
        
        return 0;
    }
    
    /**
     * Create next payment for subscription
     */
    public function createNextPayment(Subscription $subscription): SubscriptionPayment
    {
        $billingPeriodStart = Carbon::parse($subscription->next_billing_date);
        $billingPeriodEnd = $billingPeriodStart->copy();
        
        switch ($subscription->billing_cycle) {
            case 'monthly':
                $billingPeriodEnd->addMonth()->subDay();
                break;
            case 'quarterly':
                $billingPeriodEnd->addMonths(3)->subDay();
                break;
            case 'yearly':
                $billingPeriodEnd->addYear()->subDay();
                break;
        }
        
        return SubscriptionPayment::create([
            'subscription_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
            'amount' => $subscription->monthly_amount,
            'due_date' => $subscription->next_billing_date,
            'billing_period_start' => $billingPeriodStart,
            'billing_period_end' => $billingPeriodEnd,
            'status' => 'pending',
        ]);
    }
    
    /**
     * Process subscription billing for all active subscriptions
     */
    public function processBilling(Carbon $date = null): array
    {
        $date = $date ?? Carbon::today();
        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        $subscriptions = Subscription::dueForBilling($date)->get();
        
        foreach ($subscriptions as $subscription) {
            try {
                $this->createNextPayment($subscription);
                $results['processed']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Cancel subscription
     */
    public function cancelSubscription(Subscription $subscription, string $reason = null): bool
    {
        try {
            $subscription->update([
                'status' => 'cancelled',
                'end_date' => Carbon::now(),
                'notes' => $subscription->notes . "\nCancelled: " . ($reason ?? 'No reason provided'),
            ]);
            
            // Cancel all pending payments
            $subscription->pendingPayments()->update(['status' => 'cancelled']);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Pause subscription
     */
    public function pauseSubscription(Subscription $subscription, string $reason = null): bool
    {
        try {
            $subscription->update([
                'status' => 'paused',
                'notes' => $subscription->notes . "\nPaused: " . ($reason ?? 'No reason provided'),
            ]);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Resume subscription
     */
    public function resumeSubscription(Subscription $subscription): bool
    {
        try {
            $subscription->update([
                'status' => 'active',
                'notes' => $subscription->notes . "\nResumed: " . Carbon::now()->toDateTimeString(),
            ]);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get subscription statistics for a customer
     */
    public function getCustomerStats(int $customerId): array
    {
        $subscriptions = Subscription::byCustomer($customerId)->get();
        
        return [
            'total_subscriptions' => $subscriptions->count(),
            'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
            'paused_subscriptions' => $subscriptions->where('status', 'paused')->count(),
            'cancelled_subscriptions' => $subscriptions->where('status', 'cancelled')->count(),
            'total_monthly_amount' => $subscriptions->where('status', 'active')->sum('monthly_amount'),
            'overdue_payments' => SubscriptionPayment::byCustomer($customerId)->overdue()->count(),
        ];
    }
}
