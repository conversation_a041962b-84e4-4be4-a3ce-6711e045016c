<?php

namespace App\Services;

use App\Models\DevelopmentItem;
use Illuminate\Support\Facades\Auth;

class DevelopmentItemService
{
    private DevelopmentItem $model;

    public function __construct(DevelopmentItem $model)
    {
        $this->model = $model;
    }

    /**
     * Get all development items
     * 
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function all()
    {
        $query = $this->model->orderBy('category', 'asc')->orderBy('name', 'asc');
        
        // Apply active filter if not admin
        if (!Auth::user()->hasRole('admin')) {
            $query->where('is_active', true);
        }

        // Handle pagination if requested
        if (request()->query('pagination')) {
            return $query->paginate(
                request()->query('per_page', 10),
                ['*'],
                'page',
                request()->query('current_page', 1)
            );
        }

        return $query->get();
    }

    /**
     * Find a development item by ID
     * 
     * @param int $id
     * @return DevelopmentItem|null
     */
    public function find($id)
    {
        $query = $this->model->newQuery();
        
        // Apply active filter if not admin
        if (!Auth::user()->hasRole('admin')) {
            $query->where('is_active', true);
        }
        
        return $query->find($id);
    }

    /**
     * Store a new development item
     * 
     * @param array $data
     * @return DevelopmentItem
     */
    public function store($data)
    {
        return $this->model->create($data);
    }

    /**
     * Update a development item
     * 
     * @param int $id
     * @param array $data
     * @return DevelopmentItem
     * @throws \Exception
     */
    public function update($id, $data)
    {
        $item = $this->model->find($id);
        
        if (!$item) {
            throw new \Exception('Development item not found', 404);
        }
        
        $item->update($data);
        return $item->fresh();
    }

    /**
     * Delete a development item
     * 
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function delete($id)
    {
        $item = $this->model->find($id);
        
        if (!$item) {
            throw new \Exception('Development item not found', 404);
        }
        
        return $item->delete();
    }

    /**
     * Get development items by category
     * 
     * @param string $category
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByCategory($category)
    {
        $query = $this->model->where('category', $category)->orderBy('name', 'asc');
        
        // Apply active filter if not admin
        if (!Auth::user()->hasRole('admin')) {
            $query->where('is_active', true);
        }
        
        return $query->get();
    }
}
