<?php

namespace App\Services;

use App\Models\Contract;
use App\Models\ContractDraft;
use App\Models\ContractDraftFile;
use App\Models\User;
use App\Notifications\ContractDraftCreated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class ContractDraftService
{
    /**
     * Get all drafts for a contract.
     *
     * @param int $contractId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDraftsByContract($contractId)
    {
        return ContractDraft::where('contract_id', $contractId)
            ->with(['creator', 'responder', 'files'])
            ->orderBy('version', 'desc')
            ->get();
    }

    /**
     * Get a specific draft.
     *
     * @param int $id
     * @return ContractDraft|null
     */
    public function find($id)
    {
        return ContractDraft::with(['creator', 'responder', 'files', 'contract'])
            ->find($id);
    }

    /**
     * Create a new draft for a contract.
     *
     * @param array $data
     * @return ContractDraft
     */
    public function store(array $data)
    {
        // Start a database transaction
        return DB::transaction(function () use ($data) {
            $contract = Contract::findOrFail($data['contract_id']);

            // Get the latest version number for this contract's drafts
            $latestVersion = ContractDraft::where('contract_id', $data['contract_id'])
                ->max('version') ?? 0;

            // Create the draft
            $draft = ContractDraft::create([
                'contract_id' => $data['contract_id'],
                'created_by' => Auth::id(),
                'description' => $data['description'] ?? null,
                'status' => 'Pending',
                'version' => $latestVersion + 1,
            ]);

            // Process file uploads
            if (!empty($data['files'])) {
                foreach ($data['files'] as $fileData) {
                    ContractDraftFile::create([
                        'draft_id' => $draft->id,
                        'type' => 'file',
                        'file_path' => $fileData['path'],
                        'original_filename' => $fileData['original_name'],
                        'file_type' => $fileData['type'],
                        'file_size' => $fileData['size'],
                    ]);
                }
            }

            // Process URLs
            if (!empty($data['urls'])) {
                foreach ($data['urls'] as $url) {
                    ContractDraftFile::create([
                        'draft_id' => $draft->id,
                        'type' => 'url',
                        'url' => $url,
                    ]);
                }
            }

            // Load relationships for the draft
            $draft->load(['creator', 'files', 'contract']);

            return $draft;
        });
    }

    /**
     * Notify customer and admin users about a new contract draft
     *
     * @param ContractDraft $draft
     * @return void
     */
    public function notifyUsersAboutNewDraft(ContractDraft $draft): void
    {
        $recipients = collect();

        // Make sure contract relationship is loaded
        if (!$draft->relationLoaded('contract')) {
            $draft->load('contract');
        }

        // Add the customer (visitor) if exists
        if ($draft->contract->customer_id) {
            $customer = User::find($draft->contract->customer_id);
            if ($customer) {
                $recipients->push($customer);
            }
        }

        // Add admin users
        $adminUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        $recipients = $recipients->merge($adminUsers);

        // Send notification to all recipients
        Notification::send($recipients, new ContractDraftCreated($draft));
    }

    /**
     * Process a response to a draft (accept or decline).
     *
     * @param ContractDraft $draft
     * @param array $data
     * @return ContractDraft
     */
    public function processDraftResponse(ContractDraft $draft, array $data)
    {
        // Start a database transaction
        return DB::transaction(function () use ($draft, $data) {
            $contract = $draft->contract;

            // Update the draft
            $draft->update([
                'status' => $data['status'],
                'feedback' => $data['feedback'] ?? null,
                'responded_by' => Auth::id(),
                'responded_at' => now(),
            ]);

            // If the draft is accepted, update the contract status to "In Progress"
            if ($data['status'] === 'Accepted') {
                $contract->update(['status' => 'Development']);

                // Log the status change
                app(ContractService::class)->logStatusChange(
                    $contract,
                    $contract->status,
                    'Development',
                    'Contract draft accepted by customer. Moving to development.'
                );
            }

            // If the draft is declined, check if this is the third declined draft
            if ($data['status'] === 'Declined') {
                $declinedCount = ContractDraft::where('contract_id', $contract->id)
                    ->where('status', 'Declined')
                    ->count();

                // If this is the third declined draft, update the contract status to "Rejected"
                if ($declinedCount >= 3) {
                    $contract->update(['status' => 'Rejected']);

                    // Log the status change
                    app(ContractService::class)->logStatusChange(
                        $contract,
                        $contract->status,
                        'Rejected',
                        'Contract rejected after three declined drafts.'
                    );
                }
            }

            return $draft->fresh(['creator', 'responder', 'files', 'contract']);
        });
    }

    /**
     * Delete a draft file.
     *
     * @param int $id
     * @return bool
     */
    public function deleteFile($id)
    {
        $file = ContractDraftFile::findOrFail($id);

        // If it's a physical file, delete it from storage
        if ($file->type === 'file' && $file->file_path) {
            $filePath = public_path('uploads/' . $file->file_path);
            if (file_exists($filePath)) {
                @unlink($filePath);
            }
        }

        return $file->delete();
    }
}
