<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class FileUploadHelper
{
    protected $s3Service;

    public function __construct()
    {
        $this->s3Service = new S3FileUploadService();
    }

    /**
     * Process file uploads from request and return file data array
     *
     * @param Request $request
     * @param string $fieldName
     * @param string $directory
     * @return array
     */
    public function processFileUploads(Request $request, string $fieldName, string $directory): array
    {
        $files = [];
        
        if ($request->hasFile($fieldName)) {
            $uploadedFiles = is_array($request->file($fieldName)) 
                ? $request->file($fieldName) 
                : [$request->file($fieldName)];

            foreach ($uploadedFiles as $file) {
                try {
                    $result = $this->s3Service->uploadFile($file, $directory);
                    
                    if ($result) {
                        $files[] = [
                            'path' => $result['path'],
                            'url' => $result['url'],
                            'original_name' => $result['original_name'],
                            'type' => $result['file_type'],
                            'size' => $result['file_size'],
                        ];
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing file upload: ' . $e->getMessage(), [
                        'file' => $file->getClientOriginalName(),
                        'directory' => $directory
                    ]);
                }
            }
        }

        return $files;
    }

    /**
     * Process single file upload
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return array|null
     */
    public function processSingleFile(UploadedFile $file, string $directory): ?array
    {
        try {
            $result = $this->s3Service->uploadFile($file, $directory);
            
            if ($result) {
                return [
                    'path' => $result['path'],
                    'url' => $result['url'],
                    'original_name' => $result['original_name'],
                    'type' => $result['file_type'],
                    'size' => $result['file_size'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error processing single file upload: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'directory' => $directory
            ]);
        }

        return null;
    }

    /**
     * Delete files from S3
     *
     * @param array|string $paths
     * @return bool
     */
    public function deleteFiles($paths): bool
    {
        if (is_string($paths)) {
            return $this->s3Service->deleteFile($paths);
        }

        if (is_array($paths)) {
            return $this->s3Service->deleteMultipleFiles($paths);
        }

        return false;
    }

    /**
     * Get file URL from S3
     *
     * @param string $path
     * @return string|null
     */
    public function getFileUrl(string $path): ?string
    {
        return $this->s3Service->getFileUrl($path);
    }

    /**
     * Validate file before upload
     *
     * @param UploadedFile $file
     * @param array $allowedTypes
     * @param int $maxSizeMB
     * @return bool
     */
    public function validateFile(UploadedFile $file, array $allowedTypes = [], int $maxSizeMB = 10): bool
    {
        // Check if file is valid
        if (!$file->isValid()) {
            return false;
        }

        // Check file size
        if (!$this->s3Service->validateFileSize($file, $maxSizeMB)) {
            return false;
        }

        // Check file type if specified
        if (!empty($allowedTypes) && !$this->s3Service->validateFileType($file, $allowedTypes)) {
            return false;
        }

        return true;
    }

    /**
     * Get common file validation rules
     *
     * @return array
     */
    public static function getCommonFileValidationRules(): array
    {
        return [
            'image_types' => [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                'jpg', 'jpeg', 'png', 'gif', 'webp'
            ],
            'document_types' => [
                'application/pdf', 'application/msword', 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain', 'text/csv',
                'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv'
            ],
            'all_types' => [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                'application/pdf', 'application/msword', 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain', 'text/csv',
                'jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv'
            ]
        ];
    }
}
