<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\RfpFile;
use App\Models\RfpImage;

class ContactService
{
    private Contact $model;

    public function __construct(Contact $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        $perPage = request()->query('per_page') ?? 10;
        $currentPage = request()->query('current_page') ?? 1;

        $withPagination = request()->query('pagination');
        if ($withPagination) {
            return $this->model->orderBy('id', 'desc')
            ->select([
                'id',
                'contract_name',
                'contract_details',
                'page_url',
                'start_date',
                'end_date',
                'status',
                'deleted_at',
                'created_at',
                'updated_at',
                'amount',
                'rfp_status'
            ])
        
            ->paginate($perPage, ['*'], 'current_page', $currentPage);
        }
        return $this->model->orderBy('id', 'desc')
        ->select([
            'id',
            'contract_name',
            'contract_details',
            'page_url',
            'start_date',
            'end_date',
            'status',
            'deleted_at',
            'created_at',
            'updated_at',
            'amount',
            'rfp_status'
        ])->get();
    }

    public function find($id)
    {
        return $this->model->find($id);
    }

    public function store($data)
    {
        return $this->model->create($data);
    }

    
    public function storeMultiple($data)
    {
        return $this->model->insert($data);
    }

    public function update($id, $data)
    {
        $contact = $this->model->find($id);
        $contact->update($data);
        return $contact;
    }

    public function insertImages ($contract, $images) {
      
        foreach($images as $image) {
            
            $rfpFile = [
                'contract_id' => $contract->id,
                'image' => $image
            ];
            RfpImage::create($rfpFile);
        }
    }

    public function insertFiles ($contract, $files) {

        foreach($files as $file) {
            
            $rfpFile = [
                'contract_id' => $contract->id,
                'file' => $file
            ];
            RfpFile::create($rfpFile);
        }

    }

    public function delete($id)
    {
        $contact = $this->model->find($id);
        $contact->delete();
    }

    
    public function deleteRfpFile($id)
    {
        $file = RfpFile::find($id);

        if ($file->file) {
            unlink(public_path('uploads/'.$file->file));
        }

        return $file->delete();
    }

    public function deleteRfpImage($id)
    {
        $image = RfpImage::find($id);

        if ($image->image) {
            unlink(public_path('uploads/'.$image->image));
        }
        return $image->delete();
    }
}
