<?php

namespace App\Services;

use App\Models\PaymentGateway;

class PaymentGatewayService
{
    private PaymentGateway $model;

    public function __construct(PaymentGateway $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        $perPage = request()->query('per_page') ?? 10;
        $currentPage = request()->query('current_page') ?? 1;

        $query = $this->model->with('methods')->orderBy('id', 'desc');

        $withPagination = request()->query('pagination');
        if ($withPagination) {
            return $query->paginate($perPage, ['*'], 'current_page', $currentPage);
        }
        return $query->get();
    }

    public function find($id)
    {
        return $this->model->with('methods')->find($id);
    }

    public function store($data)
    {
        return $this->model->create($data);
    }


    public function storeMultiple($data)
    {
        return $this->model->insert($data);
    }

    public function update($id, $data)
    {
        $paymentGateway = $this->model->find($id);
        if (!$paymentGateway) {
            throw new \Exception('Payment Gateway not found');
        }
        $paymentGateway->update($data);
        return $paymentGateway;
    }

    public function delete($id)
    {
        $paymentGateway = $this->model->find($id);
        $paymentGateway->delete();
    }
}
