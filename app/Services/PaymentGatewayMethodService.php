<?php

namespace App\Services;

use App\Models\PaymentGatewayMethod;

class PaymentGatewayMethodService
{
    private PaymentGatewayMethod $model;

    public function __construct(PaymentGatewayMethod $model)
    {
        $this->model = $model;
    }

    /**
     * Get all methods for a specific payment gateway.
     *
     * @param int $paymentGatewayId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByPaymentGatewayId($paymentGatewayId)
    {
        return $this->model->where('payment_gateway_id', $paymentGatewayId)
            ->orderBy('method_name')
            ->get();
    }

    /**
     * Find a specific method by ID.
     *
     * @param int $id
     * @return PaymentGatewayMethod|null
     */
    public function find($id)
    {
        return $this->model->find($id);
    }

    /**
     * Store a new payment gateway method.
     *
     * @param array $data
     * @return PaymentGatewayMethod
     */
    public function store($data)
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing payment gateway method.
     *
     * @param int $id
     * @param array $data
     * @return PaymentGatewayMethod
     */
    public function update($id, $data)
    {
        $method = $this->model->find($id);
        if (!$method) {
            throw new \Exception('Payment Gateway Method not found');
        }
        $method->update($data);
        return $method;
    }

    /**
     * Delete a payment gateway method.
     *
     * @param int $id
     * @return void
     */
    public function delete($id)
    {
        $method = $this->model->find($id);
        if (!$method) {
            throw new \Exception('Payment Gateway Method not found');
        }
        $method->delete();
    }
}
