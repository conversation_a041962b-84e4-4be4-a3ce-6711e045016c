<?php

namespace App\Services;

use App\Models\Payment;

class PaymentService
{
    private Payment $model;

    public function __construct(Payment $model)
    {
        $this->model = $model;
    }

    public function all()
    {
        $perPage = request()->query('per_page') ?? 10;
        $currentPage = request()->query('current_page') ?? 1;

        $withPagination = request()->query('pagination');
        if ($withPagination) {
            return $this->model->orderBy('id', 'desc')->with('contract')->paginate($perPage, ['*'], 'current_page', $currentPage);
        }
        return $this->model->with('contract')->orderBy('id', 'desc')->get();
    }

    public function findByContractId($contractId)
    {
        return $this->model->where('contract_id', $contractId)->orderBy('id', 'desc')->get();
    }

    public function find($id)
    {
        return $this->model->find($id);
    }

    public function store($data)
    {
        return $this->model->create($data);
    }

    public function storeMultiple($data)
    {
        return $this->model->insert($data);
    }

    public function update(Payment $payment, $data)
    {
        $payment->update($data);
        return $payment;
    }

    public function delete(Payment $payment)
    {
        $payment->delete();
    }
}

