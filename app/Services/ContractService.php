<?php

namespace App\Services;

use App\Models\Contract;
use App\Models\PenguinContractStatus;
use App\Models\RfpFile;
use App\Models\RfpImage;
use App\Models\User;
use App\Models\ContractDevelopmentItem;
use App\Models\DevelopmentItem;
use App\Notifications\ContractAccepted;
use App\Notifications\ContractCompleted;
use App\Notifications\ContractRequested;
use App\Notifications\AdminCompletedContract;
use App\Notifications\ContractPaymentApproved;
use App\Notifications\ContractSentToDevelopment;
use App\Notifications\ContractInProgress;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class ContractService
{
    private Contract $model;

    public function __construct(Contract $model)
    {
        $this->model = $model;
    }

    /**
     * Get all contracts with optional filters
     *
     * @param array $filters Optional filters to apply
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function all(array $filters = [])
    {
        $perPage = request()->query('per_page') ?? 10;
        $currentPage = request()->query('current_page') ?? 1;

        // Start building the query
        $query = $this->model->orderBy('id', 'desc');

        // Apply has_parent filter if provided
        if (isset($filters['has_parent'])) {
            if ($filters['has_parent'] == 1) {
                // Filter contracts that have a parent (parent_id is not null)
                $query->whereNotNull('parent_id');
            } elseif ($filters['has_parent'] == 0) {
                // Filter contracts that don't have a parent (parent_id is null)
                $query->whereNull('parent_id');
            }
        }

        // Apply status filter if provided
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Select fields
        $query->select([
            'id',
            'unique_id',
            'parent_id',
            'contract_name',
            'contract_details',
            'page_url',
            'start_date',
            'end_date',
            'status',
            'deleted_at',
            'created_at',
            'updated_at',
            'amount'
        ]);
        $query->with('parentContract');
        // Apply pagination if requested
        $withPagination = request()->query('pagination');
        if ($withPagination) {
            return $query->paginate($perPage, ['*'], 'current_page', $currentPage);
        }

        return $query->get();
    }

    public function find($id)
    {
        return $this->model->with(['parentContract'])->find($id);
    }

    public function store($data)
    {
        // Generate unique_id if not provided
        if (!isset($data['unique_id'])) {
            $data['unique_id'] = $this->generateUniqueId();
        }

        return $this->model->create($data);
    }

    /**
     * Generate a unique ID in the format YYMMDD + sequential number
     * Example: 250426001 for the first contract on April 26, 2025
     *
     * @return string
     */
    public function generateUniqueId()
    {
        // Get current date in YYMMDD format
        $datePrefix = date('ymd');

        // Count contracts created today to get the sequence number
        $todayCount = $this->model
            ->whereDate('created_at', today())
            ->count();

        // Start with the next sequence number
        $sequenceNumber = $todayCount + 1;

        // Format the sequence number with leading zeros (e.g., 001, 012, etc.)
        $formattedSequence = str_pad($sequenceNumber, 3, '0', STR_PAD_LEFT);

        // Combine date prefix and sequence number
        $uniqueId = $datePrefix . $formattedSequence;

        // Check if this ID already exists (in case of race conditions)
        while ($this->uniqueIdExists($uniqueId)) {
            // Increment and try again
            $sequenceNumber++;
            $formattedSequence = str_pad($sequenceNumber, 3, '0', STR_PAD_LEFT);
            $uniqueId = $datePrefix . $formattedSequence;
        }

        return $uniqueId;
    }

    /**
     * Check if a unique_id already exists in the database
     *
     * @param string $uniqueId
     * @return bool
     */
    protected function uniqueIdExists(string $uniqueId): bool
    {
        return $this->model->where('unique_id', $uniqueId)->exists();
    }


    public function storeMultiple($data)
    {
        // Generate unique_id for each contract if not provided
        foreach ($data as &$contract) {
            if (!isset($contract['unique_id'])) {
                $contract['unique_id'] = $this->generateUniqueId();
            }
        }

        return $this->model->insert($data);
    }

    public function update($id, $data)
    {
        $contract = $this->model->find($id);

        // Check if status has changed
        if (isset($data['status']) && $data['status'] !== $contract->status) {
            // Get comment if provided
            $comment = $data['status_remarks'] ?? null;

            // Log status change
            $this->logStatusChange($contract, $contract->status, $data['status'], $comment);

            // Remove status_remarks from data to prevent it from being saved to the contract
            if (isset($data['status_remarks'])) {
                unset($data['status_remarks']);
            }
        }
        if (auth()->user()->hasRole('admin') && $contract->status == 'Pending') { 
            $data['status'] = "New Lead";
        }

        $contract->update($data);
        return $contract;
    }

    /**
     * Log a status change for a contract
     *
     * @param Contract $contract
     * @param string|null $previousStatus
     * @param string $newStatus
     * @param string|null $comment
     * @return PenguinContractStatus
     */
    public function logStatusChange(Contract $contract, ?string $previousStatus, string $newStatus, ?string $comment = null)
    {
        return PenguinContractStatus::create([
            'contract_id' => $contract->id,
            'status' => $newStatus,
            'comment' => $comment,
            'user_id' => Auth::id(),
        ]);
    }

    public function insertImages ($contract, $images) {
        // If images is empty, return early
        if (empty($images)) {
            return;
        }

        foreach($images as $image) {
            $rfpFile = [
                'contract_id' => $contract->id,
                'image' => $image
            ];
            RfpImage::create($rfpFile);
        }
    }

    public function insertFiles ($contract, $files) {
        // If files is empty, return early
        if (empty($files)) {
            return;
        }

        foreach($files as $file) {
            $rfpFile = [
                'contract_id' => $contract->id,
                'file' => $file
            ];
            RfpFile::create($rfpFile);
        }
    }
  /**
     * Insert development items for a contract
     *
     * @param Contract $contract
     * @param array $developmentItems
     * @return void
     */
    public function insertDevelopmentItems(Contract $contract, array $developmentItems)
    {
        // If development items is empty, return early
        if (empty($developmentItems)) {
            return;
        }

        foreach ($developmentItems as $item) {
            // Get development item details if development_item_id is provided
            $developmentItem = null;
            $itemName = $item['name'] ?? 'Custom Item';

            if (!empty($item['development_item_id'])) {
                $developmentItem = DevelopmentItem::find($item['development_item_id']);
                if ($developmentItem) {
                    $itemName = $developmentItem->name;
                }
            }

            // Create contract development item record
            ContractDevelopmentItem::create([
                'contract_id' => $contract->id,
                'development_item_id' => $item['development_item_id'] ?? null,
                'name' => $itemName,
                'price' => $item['price'] ?? 0,
                'quantity' => $item['quantity'] ?? 1,
                'total' => $item['price'] * $item['quantity'],
            ]);
        }
    }

    /**
     * Insert development items for a contract
     *
     * @param Contract $contract
     * @param array $developmentItems
     * @return void
     */
    public function removeDevelopmentItems(Contract $contract)
    {

        return ContractDevelopmentItem::where('contract_id', $contract->id)->delete();

    }

    public function delete($id)
    {
        $contract = $this->model->find($id);
        $contract->delete();
    }

    /**
     * Get contracts with specific statuses for bacbon users
     *
     * @param array $statuses
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getContractsByStatuses(array $statuses)
    {
        $perPage = request()->query('per_page') ?? 10;
        $currentPage = request()->query('current_page') ?? 1;

        $query = $this->model->whereIn('status', $statuses)
            ->orderBy('updated_at', 'desc')
            ->with([
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'statusHistory' => function($query) {
                    $query->latest()->take(1);
                }
            ]);

        $withPagination = request()->query('pagination');
        if ($withPagination) {
            return $query->paginate($perPage, ['*'], 'current_page', $currentPage);
        }

        return $query->get();
    }

    /**
     * Notify admin users about a contract being accepted
     *
     * @param Contract $contract
     * @return void
     */
    public function notifyAdminsAboutAcceptedContract(Contract $contract): void
    {
        // Get all admin users
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        // Send notification to all admins
        Notification::send($admins, new ContractAccepted($contract));
    }

    /**
     * Notify admin users about a contract being completed
     *
     * @param Contract $contract
     * @return void
     */
    public function notifyAdminsAboutCompletedContract(Contract $contract): void
    {
        // Get all admin users
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        // Send notification to all admins
        Notification::send($admins, new ContractCompleted($contract));
    }
    /**
     * Notify admin users about a contract being completed
     *
     * @param Contract $contract
     * @return void
     */
    public function notifyAdminsAboutContractRequest(Contract $contract): void
    {
        // Get all admin users
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        // Send notification to all admins
        Notification::send($admins, new ContractRequested($contract));
    }
    /**
     * Notify visitor and bacbon users about a contract being completed by admin
     *
     * @param Contract $contract
     * @param string $comment
     * @return void
     */
    public function notifyUsersAboutAdminCompletedContract(Contract $contract, string $comment): void
    {
        $recipients = collect();

        // Add the customer (visitor) if exists
        if ($contract->customer_id) {
            $customer = User::find($contract->customer_id);
            if ($customer) {
                $recipients->push($customer);
            }
        }

        // Add bacbon users
        $bacbonUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'bacbon');
        })->get();

        $recipients = $recipients->merge($bacbonUsers);

        // Send notification to all recipients
        Notification::send($recipients, new AdminCompletedContract($contract, $comment));
    }

    public function deleteRfpFile($id)
    {
        $file = RfpFile::find($id);

        if ($file->file) {
            unlink(public_path('uploads/'.$file->file));
        }

        return $file->delete();
    }

    public function deleteRfpImage($id)
    {
        $image = RfpImage::find($id);

        if ($image->image) {
            unlink(public_path('uploads/'.$image->image));
        }
        return $image->delete();
    }

    /**
     * Notify customer about their contract payment being approved
     *
     * @param Contract $contract
     * @return void
     */
    public function notifyCustomerAboutPaymentApproval(Contract $contract): void
    {
        // Get the customer
        if ($contract->customer_id) {
            $customer = User::find($contract->customer_id);
            if ($customer) {
                // Send notification to the customer
                $customer->notify(new ContractPaymentApproved($contract));
            }
        }
    }

    /**
     * Notify bacbon users about a contract being sent to development
     *
     * @param Contract $contract
     * @param string|null $comment
     * @return void
     */
    public function notifyBacbonUsersAboutDevelopment(Contract $contract, ?string $comment = null): void
    {
        // Get all bacbon users
        $bacbonUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'bacbon');
        })->get();

        // Send notification to all bacbon users
        Notification::send($bacbonUsers, new ContractSentToDevelopment($contract, $comment));
    }

    /**
     * Notify customer and admin users about a contract being moved to in-progress
     *
     * @param Contract $contract
     * @param string|null $comment
     * @return void
     */
    public function notifyUsersAboutInProgressContract(Contract $contract, ?string $comment = null): void
    {
        $recipients = collect();

        // Add the customer (visitor) if exists
        if ($contract->customer_id) {
            $customer = User::find($contract->customer_id);
            if ($customer) {
                $recipients->push($customer);
            }
        }

        // Add admin users
        $adminUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        $recipients = $recipients->merge($adminUsers);

        // Send notification to all recipients
        Notification::send($recipients, new ContractInProgress($contract, $comment));
    }
}
