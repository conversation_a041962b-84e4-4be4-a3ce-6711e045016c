<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_support_requests';

    protected $fillable = [
        'request_type',
        'title',
        'description',
        'base_construction_cost',
        'monthly_subscription_fee',
        'additional_items_total',
        'total_construction_cost',
        'name',
        'email',
        'user_id',
        'status',
    ];

    /**
     * Get the user that owns the support request.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the files for the support request.
     */
    public function files()
    {
        return $this->hasMany(SupportRequestFile::class, 'support_request_id');
    }

    /**
     * Get the development items for the support request.
     */
    public function developmentItems()
    {
        return $this->hasMany(SupportRequestDevelopmentItem::class, 'support_request_id');
    }

    /**
     * Get the casted attributes.
     */
    protected $casts = [
        'base_construction_cost' => 'decimal:2',
        'monthly_subscription_fee' => 'decimal:2',
        'additional_items_total' => 'decimal:2',
        'total_construction_cost' => 'decimal:2',
    ];
}
