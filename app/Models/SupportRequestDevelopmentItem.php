<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportRequestDevelopmentItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_support_request_development_items';

    protected $fillable = [
        'support_request_id',
        'development_item_id',
        'name',
        'price',
        'quantity',
        'total',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'quantity' => 'integer',
    ];

    /**
     * Get the support request that owns the development item.
     */
    public function supportRequest()
    {
        return $this->belongsTo(SupportRequest::class, 'support_request_id');
    }

    /**
     * Get the development item that this record is based on.
     */
    public function developmentItem()
    {
        return $this->belongsTo(DevelopmentItem::class, 'development_item_id');
    }
}
