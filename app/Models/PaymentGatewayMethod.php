<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentGatewayMethod extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_payment_gateway_methods';

    protected $fillable = [
        'payment_gateway_id',
        'method_name',
        'account_number',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the payment gateway that owns the method.
     */
    public function paymentGateway()
    {
        return $this->belongsTo(PaymentGateway::class, 'payment_gateway_id');
    }
}
