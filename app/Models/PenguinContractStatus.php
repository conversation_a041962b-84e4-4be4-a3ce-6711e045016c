<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PenguinContractStatus extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'contract_id',
        'status',
        'comment',
        'file',
        'user_id'
    ];

    protected $attributes = [
        'status' => 'New Lead'
    ];

    public function contract()
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the files for the status.
     */
    public function files()
    {
        return $this->hasMany(PenguinContractStatusFile::class, 'status_id');
    }
}

