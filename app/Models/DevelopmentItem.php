<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DevelopmentItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_development_items';

    protected $fillable = [
        'name',
        'description',
        'price',
        'unit',
        'category',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];
}
