<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserOtp extends Model
{
    protected $fillable = [
        'user_id',
        'otp',
        'expired_at',
        'is_used',
        'ip_address',
    ];


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->expired_at = now()->addMinutes(5);
            $model->ip_address = request()->ip();
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
