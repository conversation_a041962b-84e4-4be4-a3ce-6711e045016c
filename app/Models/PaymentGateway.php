<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentGateway extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_payment_gateways';

    protected $fillable = [
        'name',
        'logo',
        'remarks',
        'status'
    ];

    /**
     * Get the methods for the payment gateway.
     */
    public function methods()
    {
        return $this->hasMany(PaymentGatewayMethod::class, 'payment_gateway_id');
    }
}

