<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContractDraft extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contract_drafts';

    protected $fillable = [
        'contract_id',
        'created_by',
        'description',
        'status',
        'version',
        'feedback',
        'responded_by',
        'responded_at',
    ];

    protected $casts = [
        'responded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the contract that owns the draft.
     */
    public function contract()
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the user who created the draft.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who responded to the draft.
     */
    public function responder()
    {
        return $this->belongsTo(User::class, 'responded_by');
    }

    /**
     * Get the files for the draft.
     */
    public function files()
    {
        return $this->hasMany(ContractDraftFile::class, 'draft_id');
    }

    /**
     * Get the file attachments for the draft.
     */
    public function fileAttachments()
    {
        return $this->hasMany(ContractDraftFile::class, 'draft_id')
            ->where('type', 'file');
    }

    /**
     * Get the URL attachments for the draft.
     */
    public function urlAttachments()
    {
        return $this->hasMany(ContractDraftFile::class, 'draft_id')
            ->where('type', 'url');
    }
}
