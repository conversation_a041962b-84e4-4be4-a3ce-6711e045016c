<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DevelopmentRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_development_requests';

    protected $fillable = [
        'contract_id',
        'request_title',
        'request_details',
        'status',
        'remarks',
        'user_id',
    ];

    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

