<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class SubscriptionPayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_subscription_payments';

    protected $fillable = [
        'subscription_id',
        'customer_id',
        'payment_code',
        'amount',
        'due_date',
        'paid_date',
        'status',
        'payment_method',
        'payment_gateway',
        'transaction_id',
        'invoice_number',
        'billing_period_start',
        'billing_period_end',
        'payment_notes',
        'payment_details',
        'late_fee',
        'retry_count',
        'next_retry_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'due_date' => 'date',
        'paid_date' => 'date',
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'payment_details' => 'array',
        'retry_count' => 'integer',
        'next_retry_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_code)) {
                $payment->payment_code = static::generatePaymentCode();
            }
        });
    }

    /**
     * Generate unique payment code
     */
    public static function generatePaymentCode(): string
    {
        $datePrefix = 'PAY-' . date('ymd');
        $count = static::whereDate('created_at', today())->count() + 1;
        $sequence = str_pad($count, 3, '0', STR_PAD_LEFT);

        $code = $datePrefix . '-' . $sequence;

        // Ensure uniqueness
        while (static::where('payment_code', $code)->exists()) {
            $count++;
            $sequence = str_pad($count, 3, '0', STR_PAD_LEFT);
            $code = $datePrefix . '-' . $sequence;
        }

        return $code;
    }

    /**
     * Mark payment as paid
     */
    public function markAsPaid($transactionId = null, $paymentMethod = null, $paymentDetails = [])
    {
        $this->update([
            'status' => 'paid',
            'paid_date' => Carbon::now(),
            'transaction_id' => $transactionId,
            'payment_method' => $paymentMethod,
            'payment_details' => $paymentDetails,
        ]);

        // Update subscription's last billing date and next billing date
        $subscription = $this->subscription;
        $subscription->update([
            'last_billing_date' => $this->due_date,
            'next_billing_date' => $subscription->calculateNextBillingDate(Carbon::parse($this->due_date)),
        ]);

        return $this;
    }

    /**
     * Mark payment as partially paid with custom amount
     */
    public function markAsPartiallyPaid($paidAmount, $transactionId = null, $paymentMethod = null, $paymentDetails = [])
    {
        $originalAmount = $this->amount;
        $remainingAmount = $originalAmount - $paidAmount;

        // Update current payment as paid with the partial amount
        $this->update([
            'status' => 'paid',
            'amount' => $paidAmount,
            'paid_date' => Carbon::now(),
            'transaction_id' => $transactionId,
            'payment_method' => $paymentMethod,
            'payment_details' => array_merge($paymentDetails, [
                'original_amount' => $originalAmount,
                'paid_amount' => $paidAmount,
                'remaining_amount' => $remainingAmount,
                'partial_payment' => true
            ]),
            'payment_notes' => "Partial payment: ₩" . number_format($paidAmount) . " of ₩" . number_format($originalAmount),
        ]);

        // Update subscription's last billing date and next billing date
        $subscription = $this->subscription;
        $subscription->update([
            'last_billing_date' => $this->due_date,
            'next_billing_date' => $subscription->calculateNextBillingDate(Carbon::parse($this->due_date)),
        ]);

        return [
            'payment' => $this,
            'remaining_amount' => $remainingAmount
        ];
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => 'failed',
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => Carbon::now()->addDays(3), // Retry in 3 days
            'payment_notes' => $reason,
        ]);

        return $this;
    }

    /**
     * Check if payment is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'pending' && Carbon::parse($this->due_date)->isPast();
    }

    /**
     * Get days overdue
     */
    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return Carbon::parse($this->due_date)->diffInDays(Carbon::now());
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₩' . number_format($this->amount);
    }

    /**
     * Get total amount including late fee
     */
    public function getTotalAmountAttribute(): float
    {
        return $this->amount + $this->late_fee;
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return '₩' . number_format($this->total_amount);
    }

    /**
     * Relationships
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function customer()
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', Carbon::now());
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeBySubscription($query, $subscriptionId)
    {
        return $query->where('subscription_id', $subscriptionId);
    }

    public function scopeDueToday($query)
    {
        return $query->where('status', 'pending')
                    ->whereDate('due_date', Carbon::today());
    }
}
