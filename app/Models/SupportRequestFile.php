<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportRequestFile extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_support_request_files';

    protected $fillable = [
        'support_request_id',
        'file_path',
        'original_filename',
        'file_type',
        'file_size',
    ];

    /**
     * Get the support request that owns the file.
     */
    public function supportRequest()
    {
        return $this->belongsTo(SupportRequest::class, 'support_request_id');
    }
}
