<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class Contract extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contracts';

    protected $fillable = [
        'customer_id',
        'unique_id',
        'parent_id',
        'contract_type_id',
        'is_development_request',
        'contract_name',
        'contract_details',
        'page_url',
        'start_date',
        'end_date',
        'status',
        'payment_status',
        'amount',
        'base_construction_cost',
        'monthly_subscription_fee',
        'additional_items_total',
        'total_construction_cost',
        'created_by',
        'rfp_status',
        'application_date',
        'acceptance_date',
        'completion_date',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'amount' => 'decimal:2',
        'base_construction_cost' => 'decimal:2',
        'monthly_subscription_fee' => 'decimal:2',
        'additional_items_total' => 'decimal:2',
        'total_construction_cost' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();


        static::addGlobalScope('roleBasedFilter', function (Builder $builder) {
            if (Auth::check()) {
                $user = Auth::user();

                if ($user->hasRole('visitor')) {
                    $builder->where('customer_id', $user->id);
                }

                if ($user->hasRole('bacbon')) {
                    $builder->whereIn('status', [
                        'Development',
                        'Maintenance',
                        'Completed',
                        'Rejected'
                    ]);
                }
            }
        });

        static::creating(function ($contract) {
            if (Auth::check()) {
                $contract->created_by = Auth::id();
            }
        });

   
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'contact_id');
    }

    public function images () {
        return $this->hasMany(RfpImage::class, 'contract_id', 'id');
    }

    public function files () {
        return $this->hasMany(RfpFile::class, 'contract_id', 'id');
    }

    public function statusHistory() {
        return $this->hasMany(PenguinContractStatus::class, 'contract_id')->orderBy('created_at', 'desc');
    }

    public function customer() {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function creator() {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the drafts for the contract.
     */
    public function drafts() {
        return $this->hasMany(ContractDraft::class, 'contract_id')->orderBy('version', 'desc');
    }

    /**
     * Get the parent contract.
     */
    public function parentContract() {
        return $this->belongsTo(Contract::class, 'parent_id');
    }

    /**
     * Get the child contracts.
     */
    public function childContracts() {
        return $this->hasMany(Contract::class, 'parent_id');
    }

    /**
     * Get the development items for the contract.
     */
    public function developmentItems() {
        return $this->hasMany(ContractDevelopmentItem::class, 'contract_id');
    }

    /**
     * Get the contract type.
     */
    public function contractType() {
        return $this->belongsTo(ContractType::class, 'contract_type_id');
    }

    /**
     * Get the subscription for this contract.
     */
    public function subscription() {
        return $this->hasOne(Subscription::class, 'contract_id');
    }
}
