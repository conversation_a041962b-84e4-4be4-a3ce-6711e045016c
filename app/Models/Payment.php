<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_payments';
    protected $fillable = [
        'contact_id',
        'agreed_payment',
        'paid_amount',
        'status',
        'payment_date',
        'payment_type',
        'remarks',
        'payment_gateway_id',
        'payment_gateway_method_id',
        'payment_gateway_name',
        'payment_gateway_method_name',
    ];

    public function contract()
    {
        return $this->belongsTo(Contract::class, 'contact_id');
    }

    /**
     * Get the payment gateway associated with this payment.
     */
    public function paymentGateway()
    {
        return $this->belongsTo(PaymentGateway::class, 'payment_gateway_id');
    }

    /**
     * Get the payment gateway method associated with this payment.
     */
    public function paymentGatewayMethod()
    {
        return $this->belongsTo(PaymentGatewayMethod::class, 'payment_gateway_method_id');
    }
}

