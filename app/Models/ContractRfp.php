<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContractRfp extends Model
{
    use SoftDeletes;

    protected $table = 'penguin_contract_rfps';

    protected $casts = [
        'status' => 'string',
    ];

    protected $fillable = [
        'contract_id',
        'rfp_details',
        'rfp_image',
        'rfp_file',
        'status',
    ];

    protected $dates = [
        'deleted_at',
    ];

    public function getStatusAttribute(): string
    {
        return $this->attributes['status'];
    }

    public function setStatusAttribute(string $status): void
    {
        $this->attributes['status'] = $status;
    }
}

