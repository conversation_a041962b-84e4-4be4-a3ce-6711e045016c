<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ContractType extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contract_types';

    protected $fillable = [
        'title',
        'short_description',
        'construction_cost',
        'subscription_fee',
        'category',
        'slug',
        'is_active',
        'sort_order',
        'features',
        'icon',
        'color',
        'detailed_description',
    ];

    protected $casts = [
        'construction_cost' => 'decimal:2',
        'subscription_fee' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'features' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug when creating
        static::creating(function ($contractType) {
            if (empty($contractType->slug)) {
                $contractType->slug = Str::slug($contractType->title);
                
                // Ensure slug is unique
                $originalSlug = $contractType->slug;
                $counter = 1;
                while (static::where('slug', $contractType->slug)->exists()) {
                    $contractType->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });

        // Update slug when title changes
        static::updating(function ($contractType) {
            if ($contractType->isDirty('title') && empty($contractType->slug)) {
                $contractType->slug = Str::slug($contractType->title);
                
                // Ensure slug is unique
                $originalSlug = $contractType->slug;
                $counter = 1;
                while (static::where('slug', $contractType->slug)->where('id', '!=', $contractType->id)->exists()) {
                    $contractType->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });
    }

    /**
     * Scope to get only active contract types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('title', 'asc');
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get formatted construction cost
     */
    public function getFormattedConstructionCostAttribute()
    {
        return '₩' . number_format($this->construction_cost);
    }

    /**
     * Get formatted subscription fee
     */
    public function getFormattedSubscriptionFeeAttribute()
    {
        return '₩' . number_format($this->subscription_fee);
    }

    /**
     * Get contracts using this type
     */
    public function contracts()
    {
        return $this->hasMany(Contract::class, 'contract_type_id');
    }
}
