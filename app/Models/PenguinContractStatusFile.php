<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PenguinContractStatusFile extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'penguin_contract_status_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'status_id',
        'file_path',
        'original_filename',
        'file_type',
        'file_size',
    ];

    /**
     * Get the status that owns the file.
     */
    public function status()
    {
        return $this->belongsTo(PenguinContractStatus::class, 'status_id');
    }

    /**
     * Get the full URL for the file.
     *
     * @return string
     */
    // public function getUrlAttribute()
    // {
    //     return asset('uploads/' . $this->file_path);
    // }
}
