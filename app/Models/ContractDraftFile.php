<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContractDraftFile extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contract_draft_files';

    protected $fillable = [
        'draft_id',
        'type',
        'file_path',
        'url',
        'original_filename',
        'file_type',
        'file_size',
    ];

    /**
     * Get the draft that owns the file.
     */
    public function draft()
    {
        return $this->belongsTo(ContractDraft::class, 'draft_id');
    }

    /**
     * Get the full URL for the file.
     *
     * @return string|null
     */
    public function getFullUrlAttribute()
    {
        if ($this->type === 'url') {
            return $this->url;
        }

        if ($this->file_path) {
            return asset('uploads/' . $this->file_path);
        }

        return null;
    }
}
