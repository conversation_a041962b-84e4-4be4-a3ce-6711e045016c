<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_subscriptions';

    protected $fillable = [
        'contract_id',
        'customer_id',
        'subscription_code',
        'monthly_amount',
        'start_date',
        'end_date',
        'status',
        'billing_cycle',
        'billing_day',
        'next_billing_date',
        'last_billing_date',
        'auto_renewal',
        'grace_period_days',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'monthly_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_billing_date' => 'date',
        'last_billing_date' => 'date',
        'auto_renewal' => 'boolean',
        'grace_period_days' => 'integer',
        'billing_day' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            if (empty($subscription->subscription_code)) {
                $subscription->subscription_code = static::generateSubscriptionCode();
            }
        });
    }

    /**
     * Generate unique subscription code
     */
    public static function generateSubscriptionCode(): string
    {
        $datePrefix = 'SUB-' . date('ymd');
        $count = static::whereDate('created_at', today())->count() + 1;
        $sequence = str_pad($count, 3, '0', STR_PAD_LEFT);
        
        $code = $datePrefix . '-' . $sequence;
        
        // Ensure uniqueness
        while (static::where('subscription_code', $code)->exists()) {
            $count++;
            $sequence = str_pad($count, 3, '0', STR_PAD_LEFT);
            $code = $datePrefix . '-' . $sequence;
        }
        
        return $code;
    }

    /**
     * Calculate next billing date based on billing cycle
     */
    public function calculateNextBillingDate(Carbon $fromDate = null): Carbon
    {
        $fromDate = $fromDate ?? Carbon::parse($this->next_billing_date ?? $this->start_date);
        
        switch ($this->billing_cycle) {
            case 'monthly':
                return $fromDate->addMonth()->day($this->billing_day);
            case 'quarterly':
                return $fromDate->addMonths(3)->day($this->billing_day);
            case 'yearly':
                return $fromDate->addYear()->day($this->billing_day);
            default:
                return $fromDate->addMonth()->day($this->billing_day);
        }
    }

    /**
     * Check if subscription is overdue
     */
    public function isOverdue(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }
        
        $gracePeriodEnd = Carbon::parse($this->next_billing_date)->addDays($this->grace_period_days);
        return Carbon::now()->isAfter($gracePeriodEnd);
    }

    /**
     * Get formatted monthly amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₩' . number_format($this->monthly_amount);
    }

    /**
     * Relationships
     */
    public function contract()
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    public function customer()
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function payments()
    {
        return $this->hasMany(SubscriptionPayment::class, 'subscription_id')->orderBy('due_date', 'desc');
    }

    public function pendingPayments()
    {
        return $this->hasMany(SubscriptionPayment::class, 'subscription_id')
                    ->where('status', 'pending')
                    ->orderBy('due_date', 'asc');
    }

    public function paidPayments()
    {
        return $this->hasMany(SubscriptionPayment::class, 'subscription_id')
                    ->where('status', 'paid')
                    ->orderBy('paid_date', 'desc');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'active')
                    ->where('next_billing_date', '<', Carbon::now()->subDays(7)); // Assuming 7 days grace period
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeDueForBilling($query, Carbon $date = null)
    {
        $date = $date ?? Carbon::today();
        return $query->where('status', 'active')
                    ->where('next_billing_date', '<=', $date);
    }
}
