<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class Contact extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contracts';

    protected $fillable = [
        'customer_id',
        'unique_id',
        'contract_name',
        'contract_details',
        'page_url',
        'start_date',
        'end_date',
        'status',
        'amount',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('visitorFilter', function (Builder $builder) {
            if (Auth::check() && Auth::user()->hasRole('visitor')) {
                $builder->where('created_by', Auth::id());
            }
        });

        static::creating(function ($contact) {
            if (Auth::check()) {
                $contact->created_by = Auth::id();
            }
        });
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function images () {
        return $this->hasMany(RfpImage::class, 'contract_id', 'id');
    }

    public function files () {
        return $this->hasMany(RfpFile::class, 'contract_id', 'id');
    }
}
