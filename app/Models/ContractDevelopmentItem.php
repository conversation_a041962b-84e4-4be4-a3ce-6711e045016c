<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContractDevelopmentItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'penguin_contract_development_items';

    protected $fillable = [
        'contract_id',
        'development_item_id',
        'name',
        'price',
        'quantity',
        'total',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'quantity' => 'integer',
    ];

    /**
     * Get the contract that owns the development item.
     */
    public function contract()
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the development item that this record is based on.
     */
    public function developmentItem()
    {
        return $this->belongsTo(DevelopmentItem::class, 'development_item_id');
    }
}
