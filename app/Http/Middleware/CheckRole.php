<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $role)
    {
        if (!auth()->user() || !auth()->user()->hasRole($role)) {
            return $this->apiResponse(null, 'Forbidden! You are not authorized to access this resource.', '금지되었습니다! 이 리소스에 접근할 권한이 없습니다.', false, 403);
        }

        return $next($request);
    }

    protected function apiResponse($data = null, $message = null, $message_kr = null, $status = null, $statusCode = null)
    {
        $array = [
            'status' => $status,
            'message' => $message,
            'message_kr' => $message_kr,
            'data' => $data,
        ];

        return response()->json($array, $statusCode);
    }
}

