<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use App\Http\Requests\User\UserStoreRequest;
use App\Http\Requests\User\UserUpdateRequest;
use App\Notifications\UserCreated;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/users",
     *     tags={"Users"},
     *     summary="Get all users",
     *     @OA\Response(
     *         response=200,
     *         description="List of users"
     *     )
     * )
     */
    public function index()
    {
        $users = User::with('roles')->paginate(50);
        return $this->apiResponse($users, 'Users retrieved successfully', '사용자가 성공적으로 검색되었습니다', true, 200);
    }

    /**
     * @OA\Post(
     *     path="/api/users",
     *     tags={"Users"},
     *     summary="Create a new user",
     *     description="Create a new user with the specified role (admin, bacbon, or visitor)",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "email", "username", "password", "password_confirmation", "role"},
     *             @OA\Property(property="name", type="string", example="John Doe", description="User's full name"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="User's email address"),
     *             @OA\Property(property="username", type="string", example="johndoe", description="User's username"),
     *             @OA\Property(property="phone_no", type="string", example="01234567890", description="User's phone number"),
     *             @OA\Property(property="password", type="string", format="password", example="Password@123", description="User's password"),
     *             @OA\Property(property="password_confirmation", type="string", format="password", example="Password@123", description="Confirm password"),
     *             @OA\Property(property="role", type="string", example="visitor", description="User's role (admin, bacbon, or visitor)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User created successfully"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized - Only admin and bacbon users can create new users"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function store(UserStoreRequest $request)
    {
        try {
            // Check if the authenticated user has admin or bacbon role
            if (!auth()->user()->hasRole('admin') && !auth()->user()->hasRole('bacbon')) {
                return $this->apiResponse(
                    null,
                    'Unauthorized. Only admin and bacbon users can create new users.',
                    '권한이 없습니다. 관리자 및 백본 사용자만 새 사용자를 만들 수 있습니다.',
                    false,
                    403
                );
            }

            $data = $request->validated();

            // Store the original password for the email
            $originalPassword = $data['password'];

            // Hash the password for database storage
            $data['password'] = Hash::make($data['password']);

            // Extract role from data
            $roleName = $data['role'];
            unset($data['role']);

            $user = User::create($data);

            // Attach the selected role
            $role = Role::where('name', $roleName)->first();
            if (!$role) {
                // Fallback to visitor role if the selected role doesn't exist
                $role = Role::where('name', 'visitor')->first();
            }
            $user->roles()->attach($role);

            // Send welcome email with the original password
            $user->notify(new UserCreated($originalPassword));

            return $this->apiResponse(
                $user->load('roles'),
                'User created successfully',
                '사용자가 성공적으로 생성되었습니다',
                true,
                201
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error creating user: ' . $e->getMessage(),
                '사용자 생성 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * @OA\Get(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Get user details",
     *     @OA\Response(
     *         response=200,
     *         description="User details"
     *     )
     * )
     */
    public function show($id)
    {
        $user = User::with('roles')->find($id);

        if (!$user) {
            return $this->apiResponse(
                null,
                'User not found',
                '사용자를 찾을 수 없습니다',
                false,
                404
            );
        }

        return $this->apiResponse(
            $user,
            'User retrieved successfully',
            '사용자가 성공적으로 검색되었습니다',
            true,
            200
        );
    }

    /**
     * @OA\Put(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Update user details",
     *     @OA\Response(
     *         response=200,
     *         description="User updated successfully"
     *     )
     * )
     */
    public function update(UserUpdateRequest $request, $id)
    {
        try {
            $user = User::find($id);

            if (!$user) {
                return $this->apiResponse(
                    null,
                    'User not found',
                    '사용자를 찾을 수 없습니다',
                    false,
                    404
                );
            }

            $data = $request->validated();

            if (isset($data['password'])) {
                $data['password'] = Hash::make($data['password']);
            }

            $user->update($data);

            return $this->apiResponse(
                $user->load('roles'),
                'User updated successfully',
                '사용자가 성공적으로 업데이트되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error updating user: ' . $e->getMessage(),
                '사용자 업데이트 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Delete a user",
     *     @OA\Response(
     *         response=200,
     *         description="User deleted successfully"
     *     )
     * )
     */
    public function destroy($id)
    {
        try {
            $user = User::find($id);

            if (!$user) {
                return $this->apiResponse(
                    null,
                    'User not found',
                    '사용자를 찾을 수 없습니다',
                    false,
                    404
                );
            }

            $user->delete();

            return $this->apiResponse(
                null,
                'User deleted successfully',
                '사용자가 성공적으로 삭제되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error deleting user: ' . $e->getMessage(),
                '사용자 삭제 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * @OA\Get(
     *     path="/api/check-username/{username}",
     *     tags={"Users"},
     *     summary="Check if username exists",
     *     @OA\Response(
     *         response=200,
     *         description="Username availability status"
     *     )
     * )
     */
    public function checkUsername(Request $request)
    {
        if (!$request->has('username')) {
            return $this->apiResponse(
                null,
                'Username is required',
                '사용자 이름은 필수입니다',
                false,
                422
            );
        }

        $exists = User::where('username', $request->username)->exists();

        return $this->apiResponse(
            ['exists' => $exists],
            $exists ? 'Username is already taken' : 'Username is available',
            $exists ? '이미 사용 중인 사용자 이름입니다' : '사용 가능한 사용자 이름입니다',
            true,
            200
        );
    }

    /**
     * @OA\Get(
     * path="/api/search-users",
     * tags={"Users"},
     * summary="Search users by multiple criteria",
     * description="Search users by name, email, username, phone number, or role",
     * @OA\Parameter(
     *     name="q",
     *     in="query",
     *     description="Search query (searches in name, email, username, phone_no)",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="name",
     *     in="query",
     *     description="Search by name",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="email",
     *     in="query",
     *     description="Search by email",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="username",
     *     in="query",
     *     description="Search by username",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="phone_no",
     *     in="query",
     *     description="Search by phone number",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="role",
     *     in="query",
     *     description="Filter by role (admin, bacbon, visitor)",
     *     required=false,
     *     @OA\Schema(type="string")
     * ),
     * @OA\Parameter(
     *     name="per_page",
     *     in="query",
     *     description="Number of results per page",
     *     required=false,
     *     @OA\Schema(type="integer", default=10)
     * ),
     * @OA\Parameter(
     *     name="current_page",
     *     in="query",
     *     description="Current page number",
     *     required=false,
     *     @OA\Schema(type="integer", default=1)
     * ),
     * @OA\Parameter(
     *     name="pagination",
     *     in="query",
     *     description="Enable pagination",
     *     required=false,
     *     @OA\Schema(type="boolean", default=true)
     * ),
     * @OA\Response(
     *     response=200,
     *     description="Search results"
     * ),
     * @OA\Response(
     *     response=422,
     *     description="Validation error - At least one search parameter is required"
     * )
     * )
     */
    public function searchUser(Request $request)
    {
        try {
            // Validate that at least one search parameter is provided
            $request->validate([
                'q' => 'nullable|string|min:1|max:255',
                'name' => 'nullable|string|min:1|max:255',
                'email' => 'nullable|email|max:255',
                'username' => 'nullable|string|min:1|max:255',
                'phone_no' => 'nullable|string|min:1|max:20',
                'role' => 'nullable|string|in:admin,bacbon,visitor',
                'per_page' => 'nullable|integer|min:1|max:100',
                'current_page' => 'nullable|integer|min:1',
                'pagination' => 'nullable|string|in:true,false',
            ]);

            // Check if at least one search parameter is provided
            if (!$request->hasAny(['q', 'name', 'email', 'username', 'phone_no', 'role'])) {
                return $this->apiResponse(
                    null,
                    'At least one search parameter is required (q, name, email, username, phone_no, or role)',
                    '최소 하나의 검색 매개변수가 필요합니다 (q, name, email, username, phone_no 또는 role)',
                    false,
                    422
                );
            }

            // Start building the query
            $query = User::with('roles');

            // General search query (searches across multiple fields)
            if ($request->filled('q')) {
                $searchTerm = $request->input('q');
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('email', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('username', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('phone_no', 'LIKE', "%{$searchTerm}%");
                });
            }

            // Specific field searches
            if ($request->filled('name')) {
                $query->where('name', 'LIKE', '%' . $request->input('name') . '%');
            }

            if ($request->filled('email')) {
                $query->where('email', 'LIKE', '%' . $request->input('email') . '%');
            }

            if ($request->filled('username')) {
                $query->where('username', 'LIKE', '%' . $request->input('username') . '%');
            }

            if ($request->filled('phone_no')) {
                $query->where('phone_no', 'LIKE', '%' . $request->input('phone_no') . '%');
            }

            // Role filter
            if ($request->filled('role')) {
                $roleName = $request->input('role');
                $query->whereHas('roles', function ($q) use ($roleName) {
                    $q->where('name', $roleName);
                });
            }

            // Order by relevance (most recent first)
            $query->orderBy('created_at', 'desc');

            // Handle pagination
            $perPage = $request->input('per_page', 10);
            $currentPage = $request->input('current_page', 1);
            $withPagination = $request->input('pagination', 'true');

            // Convert string to boolean if needed
            if (is_string($withPagination)) {
                $withPagination = filter_var($withPagination, FILTER_VALIDATE_BOOLEAN);
            }

            if ($withPagination) {
                $users = $query->paginate($perPage, ['*'], 'current_page', $currentPage);
                $totalCount = $users->total();
            } else {
                $users = $query->get();
                $totalCount = $users->count();
            }

            $message = $totalCount > 0
                ? "Found {$totalCount} user(s) matching your search criteria"
                : 'No users found matching your search criteria';

            $message_kr = $totalCount > 0
                ? "검색 조건과 일치하는 {$totalCount}명의 사용자를 찾았습니다"
                : '검색 조건과 일치하는 사용자를 찾을 수 없습니다';

            return $this->apiResponse(
                $users,
                $message,
                $message_kr,
                true,
                200
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->apiResponse(
                null,
                'Validation error: ' . implode(', ', $e->validator->errors()->all()),
                '유효성 검사 오류가 발생했습니다',
                false,
                422
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error searching users: ' . $e->getMessage(),
                '사용자 검색 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }
}
