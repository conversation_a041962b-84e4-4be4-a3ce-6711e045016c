<?php

namespace App\Http\Controllers;

use App\Http\Requests\Profile\AvatarUpdateRequest;
use App\Http\Requests\Profile\PasswordUpdateRequest;
use App\Http\Requests\Profile\ProfileUpdateRequest;
use App\Services\ProfileService;
use App\Trait\HelperTrait;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    use HelperTrait;
    private ProfileService $profileService;

    public function __construct(ProfileService $profileService)
    {
        $this->profileService = $profileService;
    }

    /**
     * Get the authenticated user's profile
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        try {
            $profile = $this->profileService->getProfile();
            
            return $this->apiResponse(
                $profile,
                'Profile retrieved successfully',
                '프로필이 성공적으로 검색되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error retrieving profile: ' . $e->getMessage(),
                '프로필을 검색하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Update the authenticated user's profile
     *
     * @param ProfileUpdateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(ProfileUpdateRequest $request)
    {
        try {
            $profile = $this->profileService->updateProfile($request->validated());
            
            return $this->apiResponse(
                $profile,
                'Profile updated successfully',
                '프로필이 성공적으로 업데이트되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error updating profile: ' . $e->getMessage(),
                '프로필을 업데이트하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Update the authenticated user's avatar
     *
     * @param AvatarUpdateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAvatar(AvatarUpdateRequest $request)
    {
        try {
            $profile = $this->profileService->updateAvatar($request->file('avatar'));

            return $this->apiResponse(
                $profile,
                'Profile picture updated successfully',
                '프로필 사진이 성공적으로 업데이트되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error updating profile picture: ' . $e->getMessage(),
                '프로필 사진을 업데이트하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Update the authenticated user's password
     *
     * @param PasswordUpdateRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePassword(PasswordUpdateRequest $request)
    {
        try {
            $profile = $this->profileService->updatePassword($request->password);
            
            return $this->apiResponse(
                $profile,
                'Password updated successfully',
                '비밀번호가 성공적으로 업데이트되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error updating password: ' . $e->getMessage(),
                '비밀번호를 업데이트하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Get statistics for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            $statistics = $this->profileService->getStatistics();
            
            return $this->apiResponse(
                $statistics,
                'Statistics retrieved successfully',
                '통계가 성공적으로 검색되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error retrieving statistics: ' . $e->getMessage(),
                '통계를 검색하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }
}
