<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPayment;
use App\Models\Subscription;
use Illuminate\Http\Request;
use App\Http\Requests\SubscriptionPayment\PaymentProcessRequest;
use Carbon\Carbon;

class SubscriptionPaymentController extends Controller
{
    /**
     * Display a listing of subscription payments.
     */
    public function index(Request $request)
    {
        try {
            $query = SubscriptionPayment::with([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ]);

            // Apply filters based on user role
            if (auth()->user()->hasRole('visitor')) {
                $query->byCustomer(auth()->id());
            }

            // Apply additional filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('subscription_id')) {
                $query->bySubscription($request->input('subscription_id'));
            }

            if ($request->filled('customer_id') && auth()->user()->hasRole('admin')) {
                $query->byCustomer($request->input('customer_id'));
            }

            if ($request->filled('overdue') && $request->input('overdue') === 'true') {
                $query->overdue();
            }

            if ($request->filled('due_today') && $request->input('due_today') === 'true') {
                $query->dueToday();
            }

            // Handle pagination
            $perPage = $request->input('per_page', 10);
            $currentPage = $request->input('current_page', 1);
            $withPagination = $request->input('pagination', true);

            if (is_string($withPagination)) {
                $withPagination = filter_var($withPagination, FILTER_VALIDATE_BOOLEAN);
            }

            if ($withPagination) {
                $payments = $query->orderBy('due_date', 'desc')
                                 ->paginate($perPage, ['*'], 'current_page', $currentPage);
            } else {
                $payments = $query->orderBy('due_date', 'desc')->get();
            }

            $message = 'Subscription payments retrieved successfully';
            $message_kr = '구독 결제가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving subscription payments';
            $message_kr = '구독 결제를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Display the specified payment.
     */
    public function show($id)
    {
        try {
            $payment = SubscriptionPayment::with([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ])->find($id);

            if (!$payment) {
                $message = 'Payment not found';
                $message_kr = '결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check authorization
            if (auth()->user()->hasRole('visitor') && $payment->customer_id !== auth()->id()) {
                $message = 'Unauthorized access to payment';
                $message_kr = '결제에 대한 무단 액세스';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            $message = 'Payment retrieved successfully';
            $message_kr = '결제가 성공적으로 조회되었습니다';

            return $this->apiResponse($payment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving payment';
            $message_kr = '결제를 조회하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Process payment (mark as paid).
     */
    public function processPayment(PaymentProcessRequest $request, $id)
    {
        try {
            $payment = SubscriptionPayment::find($id);

            if (!$payment) {
                $message = 'Payment not found';
                $message_kr = '결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            if ($payment->status !== 'pending') {
                $message = 'Payment is not in pending status';
                $message_kr = '결제가 대기 상태가 아닙니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $data = $request->validated();
            $paidAmount = $data['amount'] ?? $payment->amount;
            $originalAmount = $payment->amount;

            // Check if this is a partial payment
            if ($paidAmount < $originalAmount) {
                // Only allow admin users to make partial payments
                if (!auth()->user()->hasRole('admin')) {
                    $message = 'Only admin users can process partial payments';
                    $message_kr = '관리자만 부분 결제를 처리할 수 있습니다';
                    return $this->apiResponse([], $message, $message_kr, false, 403);
                }

                // Process partial payment
                $result = $payment->markAsPartiallyPaid(
                    $paidAmount,
                    $data['transaction_id'] ?? null,
                    $data['payment_method'] ?? null,
                    $data['payment_details'] ?? []
                );

                $remainingAmount = $result['remaining_amount'];

                // Create next payment with adjusted amount (remaining amount + next month's amount)
                $subscription = $payment->subscription;
                if ($subscription->status === 'active' && $subscription->auto_renewal) {
                    $nextPayment = $this->createNextPayment($subscription);

                    // Update next payment amount to include remaining amount
                    $nextPayment->update([
                        'amount' => $subscription->monthly_amount + $remainingAmount,
                        'payment_notes' => "Includes ₩" . number_format($remainingAmount) . " remaining from previous partial payment"
                    ]);
                }

                $message = 'Partial payment processed successfully. Remaining amount added to next payment.';
                $message_kr = '부분 결제가 성공적으로 처리되었습니다. 남은 금액이 다음 결제에 추가되었습니다.';
            } else {
                // Process full payment
                $payment->markAsPaid(
                    $data['transaction_id'] ?? null,
                    $data['payment_method'] ?? null,
                    $data['payment_details'] ?? []
                );

                // Create next payment if subscription is still active
                $subscription = $payment->subscription;
                if ($subscription->status === 'active' && $subscription->auto_renewal) {
                    $this->createNextPayment($subscription);
                }

                $message = 'Payment processed successfully';
                $message_kr = '결제가 성공적으로 처리되었습니다';
            }

            // Reload payment with relationships
            $payment = $payment->fresh([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ]);

            return $this->apiResponse($payment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error processing payment';
            $message_kr = '결제 처리 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(Request $request, $id)
    {
        try {
            $payment = SubscriptionPayment::find($id);

            if (!$payment) {
                $message = 'Payment not found';
                $message_kr = '결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            if ($payment->status !== 'pending') {
                $message = 'Payment is not in pending status';
                $message_kr = '결제가 대기 상태가 아닙니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $reason = $request->input('reason', 'Payment failed');
            $payment->markAsFailed($reason);

            $message = 'Payment marked as failed';
            $message_kr = '결제가 실패로 표시되었습니다';

            return $this->apiResponse($payment->fresh(), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error marking payment as failed';
            $message_kr = '결제를 실패로 표시하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get overdue payments.
     */
    public function getOverduePayments(Request $request)
    {
        try {
            $query = SubscriptionPayment::overdue()->with([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ]);

            // Apply filters based on user role
            if (auth()->user()->hasRole('visitor')) {
                $query->byCustomer(auth()->id());
            }

            if ($request->filled('customer_id') && auth()->user()->hasRole('admin')) {
                $query->byCustomer($request->input('customer_id'));
            }

            $payments = $query->orderBy('due_date', 'asc')->get();

            $message = 'Overdue payments retrieved successfully';
            $message_kr = '연체된 결제가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving overdue payments';
            $message_kr = '연체된 결제를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get payments due today.
     */
    public function getPaymentsDueToday(Request $request)
    {
        try {
            $query = SubscriptionPayment::dueToday()->with([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ]);

            // Apply filters based on user role
            if (auth()->user()->hasRole('visitor')) {
                $query->byCustomer(auth()->id());
            }

            if ($request->filled('customer_id') && auth()->user()->hasRole('admin')) {
                $query->byCustomer($request->input('customer_id'));
            }

            $payments = $query->orderBy('due_date', 'asc')->get();

            $message = 'Payments due today retrieved successfully';
            $message_kr = '오늘 만료되는 결제가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving payments due today';
            $message_kr = '오늘 만료되는 결제를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Create next payment for subscription.
     */
    private function createNextPayment(Subscription $subscription)
    {
        $billingPeriodStart = Carbon::parse($subscription->next_billing_date);
        $billingPeriodEnd = $billingPeriodStart->copy();

        switch ($subscription->billing_cycle) {
            case 'monthly':
                $billingPeriodEnd->addMonth()->subDay();
                break;
            case 'quarterly':
                $billingPeriodEnd->addMonths(3)->subDay();
                break;
            case 'yearly':
                $billingPeriodEnd->addYear()->subDay();
                break;
        }

        return SubscriptionPayment::create([
            'subscription_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
            'amount' => $subscription->monthly_amount,
            'due_date' => $subscription->next_billing_date,
            'billing_period_start' => $billingPeriodStart,
            'billing_period_end' => $billingPeriodEnd,
            'status' => 'pending',
        ]);
    }
}
