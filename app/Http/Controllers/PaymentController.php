<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Contract;
use App\Services\PaymentService;
use Illuminate\Http\Request;

use App\Http\Requests\PaymentStoreRequest;
use App\Http\Requests\PaymentUpdateRequest;
use App\Http\Requests\Payment\ContractPaymentProcessRequest;

use G4T\Swagger\Attributes\SwaggerSection;

#[SwaggerSection('everything about your payments')]
class PaymentController extends Controller
{
    private PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    // Get all payments
    public function index(Request $request)
    {
        try {
            $payments = $this->paymentService->all();

            $message = 'Payments retrieved successfully';
            $message_kr = '연락처가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {

            $message = 'Error retrieving payments';
            $message_kr = '연락처를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $message, $message_kr, false, 500);
        }

    }

    // Find a specific payment by ID
    public function show($id)
    {
        try {
            $payment = $this->paymentService->find($id);

            if (!$payment) {
                $message = 'Payment not found';
                $message_kr = '연락처를 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $message = 'Payment retrieved successfully';
            $message_kr = '연락처가 성공적으로 조회되었습니다.';
            return $this->apiResponse($payment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving payment';
            $message_kr = '연락처를 조회하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }



    // Find all payments by contact id
    public function findByContactId($contactId)
    {
        try {
            $payments = $this->paymentService->findByContactId($contactId);

            $message = 'Payments retrieved successfully';
            $message_kr = '연락처가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {

            $message = 'Error retrieving payments';
            $message_kr = '연락처를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    // Store a single payment
    public function store(PaymentStoreRequest $request)
    {
        try {
            $validatedData = $request->validatedData();

            $payment = $this->paymentService->store($validatedData);

            $message = 'Payment created successfully';
            $message_kr = '연락처가 성공적으로 생성되었습니다.';
            return $this->apiResponse($payment, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating payment';
            $message_kr = '연락처 생성 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }



    // Update a payment
    public function update(PaymentUpdateRequest $request, Payment $payment)
    {
        try {
            $validatedData = $request->validatedData();

            $updatedPayment = $this->paymentService->update($payment, $validatedData);

            $message = 'Payment updated successfully';
            $message_kr = '연락처가 성공적으로 업데이트되었습니다.';
            return $this->apiResponse($updatedPayment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating payment';
            $message_kr = '연락처 업데이트 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    // Delete a payment
    public function destroy(Payment $payment)
    {
        try {
            $this->paymentService->delete($payment);
            $message = 'Payment deleted successfully';
            $message_kr = '연락처가 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting payment';
            $message_kr = '연락처 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Get all contract payments with filtering options
     */
    public function getContractPayments(Request $request)
    {
        try {
            $query = Payment::with([
                'contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'customer_id', 'amount', 'status', 'contract_type_id');
                },
                'contract.customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                },
                'contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                }
            ]);

            // Filter by user role - visitors can only see their own payments
            if (auth()->user()->hasRole('visitor')) {
                $query->whereHas('contract', function($q) {
                    $q->where('customer_id', auth()->id());
                });
            }

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('contract_id')) {
                $query->where('contact_id', $request->input('contract_id'));
            }

            // Only allow admin users to filter by customer_id
            if ($request->filled('customer_id') && auth()->user()->hasRole('admin')) {
                $query->whereHas('contract', function($q) use ($request) {
                    $q->where('customer_id', $request->input('customer_id'));
                });
            }

            if ($request->filled('payment_method')) {
                $query->where('payment_type', $request->input('payment_method'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('payment_date', '>=', $request->input('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('payment_date', '<=', $request->input('date_to'));
            }

            // Pagination
            $perPage = $request->input('per_page', 10);
            $payments = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $message = 'Contract payments retrieved successfully';
            $message_kr = '계약 결제가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract payments';
            $message_kr = '계약 결제를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get specific contract payment details
     */
    public function getContractPaymentDetails($id)
    {
        try {
            $query = Payment::with([
                'contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'customer_id', 'amount', 'status', 'contract_type_id', 'start_date', 'end_date');
                },
                'contract.customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                },
                'contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category', 'construction_cost', 'subscription_fee');
                },
                'contract.statusHistory' => function($query) {
                    $query->orderBy('created_at', 'desc')->limit(5);
                },
                'contract.developmentItems'
            ]);

            // Filter by user role - visitors can only see their own payments
            if (auth()->user()->hasRole('visitor')) {
                $query->whereHas('contract', function($q) {
                    $q->where('customer_id', auth()->id());
                });
            }

            $payment = $query->find($id);

            if (!$payment) {
                $message = 'Contract payment not found';
                $message_kr = '계약 결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $message = 'Contract payment details retrieved successfully';
            $message_kr = '계약 결제 세부 정보가 성공적으로 검색되었습니다';

            return $this->apiResponse($payment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract payment details';
            $message_kr = '계약 결제 세부 정보를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Process contract payment (similar to subscription payment processing)
     */
    public function processContractPayment(ContractPaymentProcessRequest $request, $id)
    {
        try {
            $query = Payment::with('contract');

            // Filter by user role - visitors can only process their own payments
            if (auth()->user()->hasRole('visitor')) {
                $query->whereHas('contract', function($q) {
                    $q->where('customer_id', auth()->id());
                });
            }

            $payment = $query->find($id);

            if (!$payment) {
                $message = 'Contract payment not found';
                $message_kr = '계약 결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if payment can be processed
            if ($payment->status === 'complete') {
                $message = 'Payment is already completed';
                $message_kr = '결제가 이미 완료되었습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            if (!in_array($payment->status, ['pending', 'partial'])) {
                $message = 'Payment cannot be processed in current status';
                $message_kr = '현재 상태에서는 결제를 처리할 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $data = $request->validated();
            $newPaymentAmount = $data['paid_amount'] ?? $payment->agreed_payment;
            $agreedAmount = $payment->agreed_payment;
            $currentPaidAmount = $payment->paid_amount ?? 0;

            // Calculate total amount that will be paid
            $totalPaidAmount = $currentPaidAmount + $newPaymentAmount;

            // Check if trying to pay more than agreed amount
            if ($totalPaidAmount > $agreedAmount) {
                $remainingAmount = $agreedAmount - $currentPaidAmount;
                $message = "Cannot pay more than agreed amount. Remaining amount: ₩" . number_format($remainingAmount);
                $message_kr = "합의된 금액보다 많이 지불할 수 없습니다. 남은 금액: ₩" . number_format($remainingAmount);
                return $this->apiResponse([
                    'agreed_amount' => $agreedAmount,
                    'current_paid_amount' => $currentPaidAmount,
                    'remaining_amount' => $remainingAmount,
                    'attempted_payment' => $newPaymentAmount,
                    'total_would_be' => $totalPaidAmount
                ], $message, $message_kr, false, 422);
            }

            // Determine final status
            $finalStatus = ($totalPaidAmount >= $agreedAmount) ? 'complete' : 'partial';

            // Create payment remarks
            $paymentRemarks = $data['remarks'] ?? '';
            if ($payment->status === 'partial') {
                $paymentRemarks .= " | Additional payment: ₩" . number_format($newPaymentAmount);
                $paymentRemarks .= " | Total paid: ₩" . number_format($totalPaidAmount) . " of ₩" . number_format($agreedAmount);
            } else {
                if ($finalStatus === 'partial') {
                    $paymentRemarks .= " | Partial payment: ₩" . number_format($totalPaidAmount) . " of ₩" . number_format($agreedAmount);
                } else {
                    $paymentRemarks .= " | Full payment completed";
                }
            }

            // Update payment
            $payment->update([
                'paid_amount' => $totalPaidAmount,
                'status' => $finalStatus,
                'payment_date' => $data['payment_date'] ?? now(),
                'payment_type' => $data['payment_method'] ?? $payment->payment_type,
                'payment_gateway_id' => $data['payment_gateway_id'] ?? null,
                'payment_gateway_method_id' => $data['payment_gateway_method_id'] ?? null,
                'payment_gateway_name' => $data['payment_gateway_name'] ?? null,
                'payment_gateway_method_name' => $data['payment_gateway_method_name'] ?? null,
                'remarks' => trim($paymentRemarks),
            ]);

            // Set appropriate message
            if ($finalStatus === 'complete') {
                $message = 'Payment completed successfully';
                $message_kr = '결제가 성공적으로 완료되었습니다';
            } else {
                $message = 'Partial payment processed successfully';
                $message_kr = '부분 결제가 성공적으로 처리되었습니다';
            }

            // Reload payment with relationships
            $payment = $payment->fresh([
                'contract.customer',
                'contract.contractType'
            ]);

            return $this->apiResponse($payment, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error processing contract payment';
            $message_kr = '계약 결제 처리 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Complete contract payment (mark as fully paid)
     */
    public function completeContractPayment(Request $request, $id)
    {
        try {
            $query = Payment::with('contract');

            // Filter by user role - visitors can only complete their own payments
            if (auth()->user()->hasRole('visitor')) {
                $query->whereHas('contract', function($q) {
                    $q->where('customer_id', auth()->id());
                });
            }

            $payment = $query->find($id);

            if (!$payment) {
                $message = 'Contract payment not found';
                $message_kr = '계약 결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            if ($payment->status === 'complete') {
                $message = 'Payment is already completed';
                $message_kr = '결제가 이미 완료되었습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $request->validate([
                'completion_notes' => 'nullable|string|max:1000',
            ]);

            $payment->update([
                'status' => 'complete',
                'paid_amount' => $payment->agreed_payment,
                'payment_date' => now(),
                'remarks' => ($payment->remarks ?? '') . " | Completed: " . ($request->input('completion_notes') ?? 'Payment marked as complete by admin'),
            ]);

            // Update contract payment status if needed
            if ($payment->contract) {
                $payment->contract->update(['payment_status' => 'Paid']);
            }

            $message = 'Contract payment completed successfully';
            $message_kr = '계약 결제가 성공적으로 완료되었습니다';

            return $this->apiResponse($payment->fresh(['contract.customer']), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error completing contract payment';
            $message_kr = '계약 결제 완료 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Negotiate contract payment amount
     */
    public function negotiateContractPayment(Request $request, $id)
    {
        try {
            $payment = Payment::with('contract')->find($id);

            if (!$payment) {
                $message = 'Contract payment not found';
                $message_kr = '계약 결제를 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $request->validate([
                'negotiated_amount' => 'required|numeric|min:0|max:999999999.99',
                'negotiation_reason' => 'required|string|max:1000',
            ]);

            $originalAmount = $payment->agreed_payment;
            $negotiatedAmount = $request->input('negotiated_amount');
            $reason = $request->input('negotiation_reason');

            $payment->update([
                'agreed_payment' => $negotiatedAmount,
                'remarks' => ($payment->remarks ?? '') . " | Negotiated: ₩" . number_format($originalAmount) . " → ₩" . number_format($negotiatedAmount) . " | Reason: " . $reason,
            ]);

            $message = 'Contract payment amount negotiated successfully';
            $message_kr = '계약 결제 금액이 성공적으로 협상되었습니다';

            return $this->apiResponse($payment->fresh(['contract.customer']), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error negotiating contract payment';
            $message_kr = '계약 결제 협상 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }
}


