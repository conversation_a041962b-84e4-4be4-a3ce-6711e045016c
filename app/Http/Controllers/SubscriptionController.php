<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\SubscriptionPayment;
use App\Models\Contract;
use Illuminate\Http\Request;
use App\Http\Requests\Subscription\SubscriptionStoreRequest;
use App\Http\Requests\Subscription\SubscriptionUpdateRequest;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of subscriptions.
     */
    public function index(Request $request)
    {
        try {
            $query = Subscription::with(['contract', 'customer']);

            // Apply filters based on user role
            if (auth()->user()->hasRole('visitor')) {
                $query->byCustomer(auth()->id());
            }

            // Apply additional filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('customer_id') && auth()->user()->hasRole('admin')) {
                $query->where('customer_id', $request->input('customer_id'));
            }

            if ($request->filled('overdue') && $request->input('overdue') === 'true') {
                $query->overdue();
            }

            // Handle pagination
            $perPage = $request->input('per_page', 10);
            $currentPage = $request->input('current_page', 1);
            $withPagination = $request->input('pagination', true);

            if (is_string($withPagination)) {
                $withPagination = filter_var($withPagination, FILTER_VALIDATE_BOOLEAN);
            }

            if ($withPagination) {
                $subscriptions = $query->orderBy('created_at', 'desc')
                                     ->paginate($perPage, ['*'], 'current_page', $currentPage);
            } else {
                $subscriptions = $query->orderBy('created_at', 'desc')->get();
            }

            $message = 'Subscriptions retrieved successfully';
            $message_kr = '구독이 성공적으로 검색되었습니다';

            return $this->apiResponse($subscriptions, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving subscriptions';
            $message_kr = '구독을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Store a newly created subscription.
     */
    public function store(SubscriptionStoreRequest $request)
    {
        try {
            $data = $request->validated();

            // Set next billing date
            $startDate = Carbon::parse($data['start_date']);
            $billingDay = $data['billing_day'] ?? 1;
            $nextBillingDate = $startDate->copy()->day($billingDay);

            // If billing day has passed this month, move to next month
            if ($nextBillingDate->isPast()) {
                $nextBillingDate->addMonth();
            }

            $data['next_billing_date'] = $nextBillingDate;

            $subscription = Subscription::create($data);

            // Create first payment record
            $this->createNextPayment($subscription);

            $message = 'Subscription created successfully';
            $message_kr = '구독이 성공적으로 생성되었습니다';

            return $this->apiResponse($subscription->load(['contract', 'customer']), $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating subscription';
            $message_kr = '구독 생성 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show($id)
    {
        try {
            $subscription = Subscription::with(['contract', 'customer', 'payments'])->find($id);

            if (!$subscription) {
                $message = 'Subscription not found';
                $message_kr = '구독을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check authorization
            if (auth()->user()->hasRole('visitor') && $subscription->customer_id !== auth()->id()) {
                $message = 'Unauthorized access to subscription';
                $message_kr = '구독에 대한 무단 액세스';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            $message = 'Subscription retrieved successfully';
            $message_kr = '구독이 성공적으로 조회되었습니다';

            return $this->apiResponse($subscription, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving subscription';
            $message_kr = '구독을 조회하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Update the specified subscription.
     */
    public function update(SubscriptionUpdateRequest $request, $id)
    {
        try {
            $subscription = Subscription::find($id);

            if (!$subscription) {
                $message = 'Subscription not found';
                $message_kr = '구독을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $data = $request->validated();
            $subscription->update($data);

            $message = 'Subscription updated successfully';
            $message_kr = '구독이 성공적으로 업데이트되었습니다';

            return $this->apiResponse($subscription->fresh()->load(['contract', 'customer']), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating subscription';
            $message_kr = '구독 업데이트 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Cancel the specified subscription.
     */
    public function cancel($id)
    {
        try {
            $subscription = Subscription::find($id);

            if (!$subscription) {
                $message = 'Subscription not found';
                $message_kr = '구독을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check authorization
            if (auth()->user()->hasRole('visitor') && $subscription->customer_id !== auth()->id()) {
                $message = 'Unauthorized to cancel this subscription';
                $message_kr = '이 구독을 취소할 권한이 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            $subscription->update([
                'status' => 'cancelled',
                'end_date' => Carbon::now(),
            ]);

            // Cancel pending payments
            $subscription->pendingPayments()->update(['status' => 'cancelled']);

            $message = 'Subscription cancelled successfully';
            $message_kr = '구독이 성공적으로 취소되었습니다';

            return $this->apiResponse($subscription->fresh(), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error cancelling subscription';
            $message_kr = '구독 취소 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get subscription payments.
     */
    public function getPayments($id, Request $request)
    {
        try {
            $subscription = Subscription::find($id);

            if (!$subscription) {
                $message = 'Subscription not found';
                $message_kr = '구독을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check authorization
            if (auth()->user()->hasRole('visitor') && $subscription->customer_id !== auth()->id()) {
                $message = 'Unauthorized access to subscription payments';
                $message_kr = '구독 결제에 대한 무단 액세스';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            $query = $subscription->payments()->with([
                'subscription' => function($query) {
                    $query->select('id', 'subscription_code', 'contract_id', 'customer_id', 'monthly_amount', 'status', 'billing_cycle', 'next_billing_date', 'auto_renewal');
                },
                'subscription.contract' => function($query) {
                    $query->select('id', 'unique_id', 'contract_name', 'contract_type_id', 'status');
                },
                'subscription.contract.contractType' => function($query) {
                    $query->select('id', 'title', 'category');
                },
                'customer' => function($query) {
                    $query->select('id', 'name', 'username', 'email');
                }
            ]);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('overdue') && $request->input('overdue') === 'true') {
                $query->overdue();
            }

            $payments = $query->orderBy('due_date', 'desc')->get();

            $message = 'Subscription payments retrieved successfully';
            $message_kr = '구독 결제가 성공적으로 검색되었습니다';

            return $this->apiResponse($payments, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving subscription payments';
            $message_kr = '구독 결제를 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Create next payment for subscription.
     */
    private function createNextPayment(Subscription $subscription)
    {
        $billingPeriodStart = Carbon::parse($subscription->next_billing_date);
        $billingPeriodEnd = $billingPeriodStart->copy();

        switch ($subscription->billing_cycle) {
            case 'monthly':
                $billingPeriodEnd->addMonth()->subDay();
                break;
            case 'quarterly':
                $billingPeriodEnd->addMonths(3)->subDay();
                break;
            case 'yearly':
                $billingPeriodEnd->addYear()->subDay();
                break;
        }

        return SubscriptionPayment::create([
            'subscription_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
            'amount' => $subscription->monthly_amount,
            'due_date' => $subscription->next_billing_date,
            'billing_period_start' => $billingPeriodStart,
            'billing_period_end' => $billingPeriodEnd,
            'status' => 'pending',
        ]);
    }
}
