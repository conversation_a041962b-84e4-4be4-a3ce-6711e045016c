<?php

namespace App\Http\Controllers;

use App\Models\ContractType;
use Illuminate\Http\Request;
use App\Http\Requests\ContractType\ContractTypeStoreRequest;
use App\Http\Requests\ContractType\ContractTypeUpdateRequest;

class ContractTypeController extends Controller
{
    /**
     * Display a listing of contract types.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = ContractType::query();

            // Apply filters
            if ($request->filled('category')) {
                $query->byCategory($request->input('category'));
            }

            if ($request->filled('is_active')) {
                $isActive = filter_var($request->input('is_active'), FILTER_VALIDATE_BOOLEAN);
                $query->where('is_active', $isActive);
            }

            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('short_description', 'LIKE', "%{$search}%")
                      ->orWhere('category', 'LIKE', "%{$search}%");
                });
            }

            // Apply ordering
            $query->ordered();

            // Handle pagination
            $perPage = $request->input('per_page', 10);
            $currentPage = $request->input('current_page', 1);
            $withPagination = $request->input('pagination', true);

            if (is_string($withPagination)) {
                $withPagination = filter_var($withPagination, FILTER_VALIDATE_BOOLEAN);
            }

            if ($withPagination) {
                $contractTypes = $query->paginate($perPage, ['*'], 'current_page', $currentPage);
            } else {
                $contractTypes = $query->get();
            }

            $message = 'Contract types retrieved successfully';
            $message_kr = '계약 유형이 성공적으로 검색되었습니다';

            return $this->apiResponse($contractTypes, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract types';
            $message_kr = '계약 유형을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Store a newly created contract type.
     *
     * @param ContractTypeStoreRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(ContractTypeStoreRequest $request)
    {
        try {
            $data = $request->validated();
            $contractType = ContractType::create($data);

            $message = 'Contract type created successfully';
            $message_kr = '계약 유형이 성공적으로 생성되었습니다';

            return $this->apiResponse($contractType, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating contract type';
            $message_kr = '계약 유형 생성 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Display the specified contract type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $contractType = ContractType::find($id);

            if (!$contractType) {
                $message = 'Contract type not found';
                $message_kr = '계약 유형을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $message = 'Contract type retrieved successfully';
            $message_kr = '계약 유형이 성공적으로 조회되었습니다';

            return $this->apiResponse($contractType, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract type';
            $message_kr = '계약 유형을 조회하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Update the specified contract type.
     *
     * @param ContractTypeUpdateRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(ContractTypeUpdateRequest $request, $id)
    {
        try {
            $contractType = ContractType::find($id);

            if (!$contractType) {
                $message = 'Contract type not found';
                $message_kr = '계약 유형을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            $data = $request->validated();
            $contractType->update($data);

            $message = 'Contract type updated successfully';
            $message_kr = '계약 유형이 성공적으로 업데이트되었습니다';

            return $this->apiResponse($contractType->fresh(), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating contract type';
            $message_kr = '계약 유형 업데이트 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Remove the specified contract type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $contractType = ContractType::find($id);

            if (!$contractType) {
                $message = 'Contract type not found';
                $message_kr = '계약 유형을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if contract type is being used by any contracts
            if ($contractType->contracts()->exists()) {
                $message = 'Cannot delete contract type that is being used by existing contracts';
                $message_kr = '기존 계약에서 사용 중인 계약 유형은 삭제할 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $contractType->delete();

            $message = 'Contract type deleted successfully';
            $message_kr = '계약 유형이 성공적으로 삭제되었습니다';

            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting contract type';
            $message_kr = '계약 유형 삭제 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get contract types by category.
     *
     * @param string $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCategory($category)
    {
        try {
            $contractTypes = ContractType::active()
                ->byCategory($category)
                ->ordered()
                ->get();

            $message = "Contract types for category '{$category}' retrieved successfully";
            $message_kr = "'{$category}' 카테고리의 계약 유형이 성공적으로 검색되었습니다";

            return $this->apiResponse($contractTypes, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract types by category';
            $message_kr = '카테고리별 계약 유형을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get all active contract types.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActive()
    {
        try {
            $contractTypes = ContractType::active()->ordered()->get();

            $message = 'Active contract types retrieved successfully';
            $message_kr = '활성 계약 유형이 성공적으로 검색되었습니다';

            return $this->apiResponse($contractTypes, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving active contract types';
            $message_kr = '활성 계약 유형을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }
}
