<?php

namespace App\Http\Controllers;

use App\Http\Requests\PaymentGatewayStoreRequest;
use App\Services\PaymentGatewayService;
use Illuminate\Http\JsonResponse;

class PaymentGatewayController extends Controller
{
    private PaymentGatewayService $service;

    public function __construct(PaymentGatewayService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the payment gateways.
     */
    public function index(): JsonResponse
    {
        try {
            $paymentGateways = $this->service->all();
            $message = 'Payment Gateways retrieved successfully.';
            $message_kr = '결제 게이트웨이가 성공적으로 검색되었습니다.';
            return $this->apiResponse($paymentGateways, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to retrieve payment gateways.';
            $message_kr = '결제 게이트웨이를 검색하지 못했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Store a newly created payment gateway in storage.
     */
    public function store(PaymentGatewayStoreRequest $request)
    {
        try {
            $data = $request->validatedData();
            $paymentGateway = $this->service->store($data);
            $message = 'Payment Gateway created successfully.';
            $message_kr = '결제 게이트웨이가 성공적으로 생성되었습니다.';
            return $this->apiResponse($paymentGateway, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Failed to create payment gateway.';
            $message_kr = '결제 게이트웨이를 생성하지 못했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Display the specified payment gateway.
     */
    public function show($id)
    {
        try {
            $paymentGateway = $this->service->find($id);
            if (!$paymentGateway) {
                $message = 'Payment Gateway not found.';
                $message_kr = '결제 게이트웨이를 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }
            $message = 'Payment Gateway retrieved successfully.';
            $message_kr = '결제 게이트웨이가 성공적으로 검색되었습니다.';
            return $this->apiResponse($paymentGateway, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to retrieve payment gateway.';
            $message_kr = '결제 게이트웨이를 검색하지 못했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Update the specified payment gateway in storage.
     */
    public function update(PaymentGatewayStoreRequest $request, $id): JsonResponse
    {
        try {
            $data = $request->validatedData();
            $paymentGateway = $this->service->update($id, $data);
            $message = 'Payment Gateway updated successfully.';
            $message_kr = '결제 게이트웨이가 성공적으로 업데이트되었습니다.';
            return $this->apiResponse($paymentGateway, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Failed to update payment gateway.';
            $message_kr = '결제 게이트웨이를 업데이트하지 못했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Remove the specified payment gateway from storage.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $this->service->delete($id);
            $message = 'Payment Gateway deleted successfully.';
            $message_kr = '결제 게이트웨이가 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to delete payment gateway.';
            $message_kr = '결제 게이트웨이를 삭제하지 못했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    /**
     * Custom API response format.
     */

}
