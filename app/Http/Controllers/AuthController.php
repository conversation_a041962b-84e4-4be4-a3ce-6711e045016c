<?php

namespace App\Http\Controllers;

use Google_Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Role;
use App\Models\UserOtp;
use Illuminate\Support\Facades\Hash;
use App\Notifications\ForgotPassword;
use Laravel\Socialite\Facades\Socialite;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ForgotUsernameRequest;
use App\Http\Requests\Auth\VerifyOtpRequest;
use App\Http\Requests\Auth\VerifySecretRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\LogMeInRequest;
use App\Http\Requests\Auth\GoogleLoginRequest;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use App\Notifications\UserCreated;

use Illuminate\Support\Facades\Http;



class AuthController extends Controller
{

   
    public function register(RegisterRequest $request)
    {
    

        // Store the original password for the email
        $originalPassword = $request->password;

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'phone_no' => $request->phone_no,
            'password' => bcrypt($request->password),
        ]);

        $role = Role::where('name', 'visitor')->first();
        $user->roles()->attach($role);

        // Send welcome email with the original password
        $user->notify(new UserCreated($originalPassword));

        $token = $user->createToken('auth_token', ['*'], now()->addDay())->plainTextToken;

        $user->load('roles');

        

        $message = 'User registered successfully';
        $message_kr = '사용자가 성공적으로 등록되었습니다';
        return $this->apiResponse([
            'token' => $token,
            'user' => $user,
        ], $message, $message_kr, true, 200);
    }


    public function login(LoginRequest $request)
    {


        $user = User::where('email', $request->login)->orWhere('username', $request->login)->first();

        if (!$user) {
            $message = 'User not found. Please verify your details and try again';
            $message_kr = '사용자를 찾을 수 없습니다. 세부 정보를 확인하고 다시 시도하세요.';
            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        if (!Hash::check($request->password, $user->password)) {

            $message = 'Password does not match';
            $message_kr = '비밀번호가 일치하지 않습니다';
            return $this->apiResponse([], $message, $message_kr, false, 422);
        }

        // Set token expiration based on remember_me flag
        $expiresAt = $request->remember_me ? now()->addYear() : now()->addHours(24);

        $token = $user->createToken('auth_token', ['*'], $expiresAt)->plainTextToken;

        $user->load('roles');

        $message = 'Successfully logged in';
        $message_kr = '성공적으로 로그인되었습니다';
        return $this->apiResponse([
            'token' => $token,
            'user' => $user,
        ], $message, $message_kr, true, 200);

    }


  
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        $message = 'Successfully logged out';
        $message_kr = '성공적으로 로그아웃되었습니다';
        return $this->apiResponse([], $message, $message_kr, true, 200);
    }


   
    public function forgotPassword(ForgotPasswordRequest $request)
    {
        $email = $request->email;
        $user = User::where('email', $email)->orWhere('username', $email)->first();
        if (!$user) {

            $message = 'No user found with this email';
            $message_kr = '이 이메일로 사용자를 찾을 수 없습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        $user->sendForgotPasswordNotification();

        $message = 'Email sent successfully';
        $message_kr = '이메일이 성공적으로 전송되었습니다';
        return $this->apiResponse([], $message, $message_kr, true, 200);
    }

    public function forgotUserName(ForgotUsernameRequest $request)
    {
        $email = $request->email;
        $user = User::where('email', $email)->first();
        if (!$user) {

            $message = 'No user found with this email';
            $message_kr = '이 이메일로 사용자를 찾을 수 없습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        $user->sendForgotUserNameNotification();

        $message = 'Email sent successfully';
        $message_kr = '이메일이 성공적으로 전송되었습니다';
        return $this->apiResponse([], $message, $message_kr, true, 200);
    }

  
    public function verifyOtp (VerifyOtpRequest $request)
    {
        $otp = $request->otp;
        $email = $request->email;
        $user = User::where('email', $email)->first();

        if (!$user) {
            $message = 'No user found with this email';
            $message_kr = '이 이메일로 사용자를 찾을 수 없습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        $userOtp = UserOtp::where('user_id', $user->id)->where('otp', $otp)->where('is_used', 0)->where('expired_at', '>', now())->first();
        if (!$userOtp) {
            $message = 'Invalid OTP';
            $message_kr = '잘못된 OTP';
            return $this->apiResponse([], $message, $message_kr, false, 422);
        }

        $userOtp->update([
            'is_used' => 1,
        ]);

        $message = 'OTP verified successfully';
        $message_kr = 'OTP가 성공적으로 검증되었습니다';

        return $this->apiResponse([
            'user' => $user,
            'otp_id' => $userOtp->id
        ], $message, $message_kr, true, 200);
    }



    public function verifySecret (VerifySecretRequest $request)
    {
        $otp = $request->secret;
        $username = $request->username;
        $user = User::where('username', $username)->first();

        if (!$user) {
            $message = 'No user found with this email';
            $message_kr = '이 이메일로 사용자를 찾을 수 없습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        $userOtp = UserOtp::where('user_id', $user->id)->where('otp', $otp)->where('is_used', 0)->where('expired_at', '>', now())->first();
        if (!$userOtp) {
            $message = 'This URL is not valid';
            $message_kr = '잘못된 URL입니다';
            return $this->apiResponse([], $message, $message_kr, false, 422);
        }

        $userOtp->update([
            'is_used' => 1,
        ]);
        $message = 'Update your password now';
        $message_kr = '지금 암호를 업데이트하세요';

        return $this->apiResponse([
            'user' => [ 'id' => $user->id, 'username' => $user->username],
            'otp_id' => $userOtp->id
        ], $message, $message_kr, true, 200);
    }



    public function resetPassword(ResetPasswordRequest $request) {

        $userOtp = UserOtp::find($request->id);
        if (!$userOtp) {

            $message = 'Something went wrong';
            $message_kr = '문제가 발생했습니다';

            return $this->apiResponse([], $message, $message_kr, false, 404);
        }
        $user = User::where('id', $userOtp->user_id)->first();

        if (!$user) {
            $message = 'No user found';
            $message_kr = '사용자를 찾을 수 없습니다';

            return $this->apiResponse([], $message, $message_kr, false, 404);
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);


        $message = 'Password has been reset successfully';
        $message_kr = '비밀번호가 성공적으로 재설정되었습니다';

        return $this->apiResponse([], $message, $message_kr, true, 200);

    }


    public function kakaoLogin (Request $request) {

            $query = http_build_query([
                'client_id' => config('globalvalue.kakao_client_id'),
                'redirect_uri' => config('globalvalue.kakao_redirect_uri'),
                'response_type' => 'code',
            ]);

            return redirect("https://kauth.kakao.com/oauth/authorize?$query");

    }


    public function kakaoCallback (Request $request) {

            // Ensure the 'code' parameter exists in the request
            if (!$request->has('code')) {
                return response()->json(['error' => 'Authorization code not found'], 400);
            }

            // Send POST request to Kakao API for OAuth token
            $response = Http::asForm()->post('https://kauth.kakao.com/oauth/token', [
                'grant_type' => 'authorization_code',
                'client_id' => config('globalvalue.kakao_client_id'),
                'client_secret' => config('globalvalue.kakao_client_secret'),
                'redirect_uri' => config('globalvalue.kakao_redirect_uri'),
                'code' => $request->get('code'),
            ]);

            // Check if the response from Kakao API is successful
            if ($response->failed()) {
                return response()->json([
                    'error' => 'Failed to fetch access token from Kakao',
                    'details' => $response->json(),
                ], $response->status());
            }


            $result = $response->json();
            // return $result['access_token'];
            $accessToken = $response->json()['access_token'];

            $userResponse = Http::withHeaders([
                'Authorization' => "Bearer $accessToken",
            ])->get('https://kapi.kakao.com/v2/user/me');

            $user = $userResponse->json();


            // Login or register the user
            $defaultPassword = 'thisismasterpassword';
            $isNewUser = !User::where('username', $user['id'])->exists();

            $authUser = User::firstOrCreate(
                ['username' => $user['id']],
                [
                    'name' => $user['properties']['nickname'],
                    'password' => bcrypt($defaultPassword),
                ]
            );

            if (!$authUser->hasRole('visitor')) {
                $role = Role::where('name', 'visitor')->first();
                $authUser->roles()->attach($role);
            }

            // If this is a new user, send welcome email with the password
            if ($isNewUser) {
                $authUser->notify(new UserCreated($defaultPassword));
            }

            $token = $authUser->createToken('authToken', ['*'], now()->addDay())->plainTextToken;

            // $message = 'Successfully logged in';
            // $message_kr = '성공적으로 로그인되었습니다';
            // return $this->apiResponse([
            //     'token' => $token,
            //     'user' => $authUser,
            // ], $message, $message_kr, true, 200);


            return redirect()->away('https://yellowpenguin.co.kr/kakao-confirmation/'.$token);

    }


    public function logMeIn(LogMeInRequest $request)
    {

        // Retrieve the token and the associated user
        $token = PersonalAccessToken::findToken($request->token);

        if (!$token) {
            return $this->apiResponse(
                null,
                'Invalid token',
                '유효하지 않은 토큰입니다',
                false,
                401
            );
        }

        // Get the user associated with the token
        $user = $token->tokenable;

        $user->load('roles');
        // Return the user and token in the response
        $message = 'Successfully logged in';
        $message_kr = '성공적으로 로그인되었습니다';

        return $this->apiResponse([
            'token' => $request->token,
            'user' => $user,
        ], $message, $message_kr, true, 200);
    }


    public function googleLogin (GoogleLoginRequest $request) {
        $googleToken = $request->token;

        try {
            $googleUser = Socialite::driver('google')->stateless()->userFromToken($googleToken);

            // Find or create a user in your database
            $defaultPassword = 'Google@' . substr(md5(time()), 0, 8); // Generate a random password
            $isNewUser = !User::where('email', $googleUser->getEmail())->exists();

            $user = User::firstOrCreate(
                ['email' => $googleUser->getEmail()],
                [
                    'name' => $googleUser->getName(),
                    'avatar' => $googleUser->getAvatar(),
                    'username' => explode('@', $googleUser->getEmail())[0] . rand(100, 999), // Generate a username
                    'password' => bcrypt($defaultPassword)
                ]
            );

            if (!$user->hasRole('visitor')) {
                $role = Role::where('name', 'visitor')->first();
                $user->roles()->attach($role);
            }

            // If this is a new user, send welcome email with the password
            if ($isNewUser) {
                $user->notify(new UserCreated($defaultPassword));
            }

            $user->load('roles');
            // Generate a personal access token
            $token = $user->createToken('authToken', ['*'], now()->addDay())->plainTextToken;


            $message = 'Successfully logged in';
            $message_kr = '성공적으로 로그인되었습니다';
            return $this->apiResponse([
                'token' => $token,
                'user' => $user,
            ], $message, $message_kr, true, 200);

            // return response()->json(['token' => $token, 'user' => $user]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid Google token'], 401);
        }
    }



    public function googleCallback(Request $request)
    {
        try {
            $client = new Google_Client(['client_id' => env('GOOGLE_CLIENT_ID')]);
            $payload = $client->verifyIdToken($request->token);

            if ($payload) {
                $googleUser = $payload; // The decoded user information from Google

                // Find or create the user in the database
                $user = User::firstOrCreate(
                    ['email' => $googleUser['email']],
                    [
                        'username' => $googleUser['email'],
                        'name' => $googleUser['name'],
                        'avatar' => $googleUser['picture'],
                        'password' => bcrypt('default_password'),
                    ]
                );

                // Generate a personal access token for the user
                $token = $user->createToken('authToken')->plainTextToken;

                if (!$user->hasRole('visitor')) {
                    $role = Role::where('name', 'visitor')->first();
                    $user->roles()->attach($role);
                }

                $user->load('roles');
                $message = 'Successfully logged in';
                $message_kr = '성공적으로 로그인되었습니다';
                return $this->apiResponse([
                    'token' => $token,
                    'user' => $user,
                ], $message, $message_kr, true, 200);

            } else {
                return response()->json(['error' => 'Invalid token'], 422);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Unable to authenticate with Google'], 422);
        }
    }

}
