<?php

namespace App\Http\Controllers;

use App\Http\Requests\DevelopmentItem\DevelopmentItemStoreRequest;
use App\Http\Requests\DevelopmentItem\DevelopmentItemUpdateRequest;
use App\Services\DevelopmentItemService;
use Illuminate\Http\Request;

class DevelopmentItemController extends Controller
{
    private DevelopmentItemService $service;

    public function __construct(DevelopmentItemService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the development items.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $items = $this->service->all();
            
            return $this->apiResponse(
                $items,
                'Development items retrieved successfully',
                '개발 항목이 성공적으로 검색되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error retrieving development items: ' . $e->getMessage(),
                '개발 항목을 검색하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Store a newly created development item in storage.
     *
     * @param  \App\Http\Requests\DevelopmentItem\DevelopmentItemStoreRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(DevelopmentItemStoreRequest $request)
    {
        try {
            $item = $this->service->store($request->validated());
            
            return $this->apiResponse(
                $item,
                'Development item created successfully',
                '개발 항목이 성공적으로 생성되었습니다',
                true,
                201
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error creating development item: ' . $e->getMessage(),
                '개발 항목을 생성하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Display the specified development item.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $item = $this->service->find($id);
            
            if (!$item) {
                return $this->apiResponse(
                    null,
                    'Development item not found',
                    '개발 항목을 찾을 수 없습니다',
                    false,
                    404
                );
            }
            
            return $this->apiResponse(
                $item,
                'Development item retrieved successfully',
                '개발 항목이 성공적으로 검색되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error retrieving development item: ' . $e->getMessage(),
                '개발 항목을 검색하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Update the specified development item in storage.
     *
     * @param  \App\Http\Requests\DevelopmentItem\DevelopmentItemUpdateRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(DevelopmentItemUpdateRequest $request, $id)
    {
        try {
            $item = $this->service->update($id, $request->validated());
            
            return $this->apiResponse(
                $item,
                'Development item updated successfully',
                '개발 항목이 성공적으로 업데이트되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            if ($e->getCode() === 404) {
                return $this->apiResponse(
                    null,
                    'Development item not found',
                    '개발 항목을 찾을 수 없습니다',
                    false,
                    404
                );
            }
            
            return $this->apiResponse(
                null,
                'Error updating development item: ' . $e->getMessage(),
                '개발 항목을 업데이트하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Remove the specified development item from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $this->service->delete($id);
            
            return $this->apiResponse(
                null,
                'Development item deleted successfully',
                '개발 항목이 성공적으로 삭제되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            if ($e->getCode() === 404) {
                return $this->apiResponse(
                    null,
                    'Development item not found',
                    '개발 항목을 찾을 수 없습니다',
                    false,
                    404
                );
            }
            
            return $this->apiResponse(
                null,
                'Error deleting development item: ' . $e->getMessage(),
                '개발 항목을 삭제하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }

    /**
     * Get development items by category.
     *
     * @param  string  $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCategory($category)
    {
        try {
            $items = $this->service->getByCategory($category);
            
            return $this->apiResponse(
                $items,
                'Development items retrieved successfully',
                '개발 항목이 성공적으로 검색되었습니다',
                true,
                200
            );
        } catch (\Exception $e) {
            return $this->apiResponse(
                null,
                'Error retrieving development items: ' . $e->getMessage(),
                '개발 항목을 검색하는 중 오류가 발생했습니다',
                false,
                500
            );
        }
    }
}
