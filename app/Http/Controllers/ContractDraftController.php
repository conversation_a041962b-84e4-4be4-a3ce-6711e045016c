<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContractDraft\ContractDraftResponseRequest;
use App\Http\Requests\ContractDraft\ContractDraftStoreRequest;
use App\Models\ContractDraft;
use App\Models\ContractDraftFile;
use App\Services\ContractDraftService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContractDraftController extends Controller
{
    private ContractDraftService $draftService;

    public function __construct(ContractDraftService $draftService)
    {
        $this->draftService = $draftService;
    }

    /**
     * Get all drafts for a contract.
     *
     * @param int $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDraftsByContract($contractId)
    {
        try {
            $drafts = $this->draftService->getDraftsByContract($contractId);

            $message = 'Contract drafts retrieved successfully';
            $message_kr = '계약 초안이 성공적으로 검색되었습니다';

            return $this->apiResponse($drafts, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract drafts';
            $message_kr = '계약 초안을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Get a specific draft.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $draft = $this->draftService->find($id);

            if (!$draft) {
                $message = 'Draft not found';
                $message_kr = '초안을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if user has permission to view this draft
            if (!$this->canAccessDraft($draft)) {
                $message = 'You do not have permission to view this draft';
                $message_kr = '이 초안을 볼 수 있는 권한이 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            $message = 'Draft retrieved successfully';
            $message_kr = '초안이 성공적으로 검색되었습니다';

            return $this->apiResponse($draft, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving draft';
            $message_kr = '초안을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Create a new draft.
     *
     * @param ContractDraftStoreRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(ContractDraftStoreRequest $request)
    {
        try {
            $data = $request->validatedData();
            $draft = $this->draftService->store($data);

            // Send notification to customer and admin users
            $this->draftService->notifyUsersAboutNewDraft($draft);

            $message = 'Draft created successfully';
            $message_kr = '초안이 성공적으로 생성되었습니다';

            return $this->apiResponse($draft, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating draft';
            $message_kr = '초안을 생성하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Respond to a draft (accept or decline).
     *
     * @param ContractDraftResponseRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondToDraft(ContractDraftResponseRequest $request, $id)
    {
        try {
            $draft = ContractDraft::find($id);

            if (!$draft) {
                $message = 'Draft not found';
                $message_kr = '초안을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if draft is already responded to
            if ($draft->status !== 'Pending') {
                $message = 'This draft has already been responded to';
                $message_kr = '이 초안은 이미 응답되었습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $data = $request->validatedData();
            $updatedDraft = $this->draftService->processDraftResponse($draft, $data);

            $statusMessage = $data['status'] === 'Accepted' ? 'accepted' : 'declined';
            $statusMessage_kr = $data['status'] === 'Accepted' ? '수락되었습니다' : '거부되었습니다';

            $message = "Draft {$statusMessage} successfully";
            $message_kr = "초안이 성공적으로 {$statusMessage_kr}";

            return $this->apiResponse($updatedDraft, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error responding to draft';
            $message_kr = '초안에 응답하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Delete a draft file.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFile($id)
    {
        try {
            $file = ContractDraftFile::find($id);

            if (!$file) {
                $message = 'File not found';
                $message_kr = '파일을 찾을 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if user has permission to delete this file
            if (!$this->canAccessDraft($file->draft)) {
                $message = 'You do not have permission to delete this file';
                $message_kr = '이 파일을 삭제할 수 있는 권한이 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Only allow deletion if draft is still pending
            if ($file->draft->status !== 'Pending') {
                $message = 'Files cannot be deleted after the draft has been responded to';
                $message_kr = '초안이 응답된 후에는 파일을 삭제할 수 없습니다';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            $this->draftService->deleteFile($id);

            $message = 'File deleted successfully';
            $message_kr = '파일이 성공적으로 삭제되었습니다';

            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting file';
            $message_kr = '파일을 삭제하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Check if the current user can access a draft.
     *
     * @param ContractDraft $draft
     * @return bool
     */
    private function canAccessDraft(ContractDraft $draft): bool
    {
        // Admin can access any draft
        if (Auth::user()->hasRole('admin')) {
            return true;
        }

        // Bacbon users can access any draft
        if (Auth::user()->hasRole('bacbon')) {
            return true;
        }

        // Visitor can only access drafts for their own contracts
        if (Auth::user()->hasRole('visitor')) {
            return $draft->contract->customer_id === Auth::id();
        }

        return false;
    }
}
