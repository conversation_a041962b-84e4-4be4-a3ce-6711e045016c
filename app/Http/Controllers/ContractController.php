<?php

namespace App\Http\Controllers;

use App\Models\Contract;
use App\Services\ContractService;
use App\Services\FileUploadHelper;
use Illuminate\Http\Request;

use App\Http\Requests\Contract\ContractStoreRequest;
use App\Http\Requests\Contract\MakeRequestStoreRequest;
use App\Http\Requests\Contract\ContractUpdateRequest;
use App\Http\Requests\Contract\ContractMultiStoreRequest;
use App\Http\Requests\Contract\ContractAcceptRequest;
use App\Http\Requests\Contract\ContractApproveRequest;
use App\Http\Requests\Contract\AdminCompleteContractRequest;
use App\Http\Requests\Contract\VisitorContractRequestStoreRequest;
use App\Http\Requests\Contract\VisitorContractStoreRequest;
use App\Http\Requests\Contract\ContractSubmissionRequest;
use App\Services\SubscriptionService;

use App\Http\Resources\Contract\DetailsResource;
use G4T\Swagger\Attributes\SwaggerSection;

#[SwaggerSection('everything about your contracts')]
class ContractController extends Controller
{
    private ContractService $contractService;
    private SubscriptionService $subscriptionService;

    public function __construct(ContractService $contractService, SubscriptionService $subscriptionService)
    {
        $this->contractService = $contractService;
        $this->subscriptionService = $subscriptionService;
    }

    // Get all contracts
    public function index(Request $request)
    {
        try {
            // Get filters from request
            $filters = [];

            // Check if has_parent filter is provided
            if ($request->has('has_parent')) {
                $filters['has_parent'] = $request->input('has_parent');
            }

            // Check if status filter is provided
            if ($request->has('status')) {
                $filters['status'] = $request->input('status');
            }

            $contracts = $this->contractService->all($filters);

            $message = 'Contracts retrieved successfully';
            $message_kr = '계약이 성공적으로 검색되었습니다';

            return $this->apiResponse($contracts, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contracts';
            $message_kr = '계약을 검색하는 중 오류가 발생했습니다';

            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    // Find a specific contract by ID
    public function show($id)
    {
        try {
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Load relationships for the contract
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments',
                'drafts',
                'drafts.files',
                'drafts.fileAttachments',
                'drafts.urlAttachments',
                'developmentItems',
                'parentContract',
                'contractType',
                'childContracts',
                'subscription',
                'subscription.payments' => function($query) {
                    $query->orderBy('due_date', 'desc');
                }
            ]);

            $message = 'Contract retrieved successfully';
            $message_kr = '계약이 성공적으로 조회되었습니다.';
            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract';
            $message_kr = '계약을 조회하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }


    public function makeNewRequest(MakeRequestStoreRequest $request)
    {
        try {
            $data = $request->validatedData();

            // Create the contract
            $contract = $this->contractService->store($data['contract']);

            // Add files and images if any
            if (!empty($data['rfp_files'])) {
                $this->contractService->insertFiles($contract, $data['rfp_files']);
            }

            if (!empty($data['rfp_images'])) {
                $this->contractService->insertImages($contract, $data['rfp_images']);
            }

            // Create initial status entry
            \App\Models\PenguinContractStatus::create([
                'contract_id' => $contract->id,
                'status' => $contract->status,
                'comment' => 'Initial contract status',
                'user_id' => auth()->id(),
            ]);


            $this->contractService->notifyAdminsAboutContractRequest($contract);


            $message = 'Contract created successfully';
            $message_kr = '계약이 성공적으로 생성되었습니다.';
            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating contract';
            $message_kr = '계약 생성 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }
    // Store a single contract
    public function store(ContractStoreRequest $request)
    {
        try {
            $data = $request->validatedData();

            // Create the contract
            $contract = $this->contractService->store($data['contract']);

            // Add development items if any
            if (!empty($data['development_items'])) {
                $this->contractService->insertDevelopmentItems($contract, $data['development_items']);
            }

            // Add files and images if any
            if (!empty($data['rfp_files'])) {
                $this->contractService->insertFiles($contract, $data['rfp_files']);
            }

            if (!empty($data['rfp_images'])) {
                $this->contractService->insertImages($contract, $data['rfp_images']);
            }

            // Create initial status entry
            \App\Models\PenguinContractStatus::create([
                'contract_id' => $contract->id,
                'status' => $contract->status,
                'comment' => 'Initial contract status' . ($data['has_support_request'] ? ' with development items' : ''),
                'user_id' => auth()->id(),
            ]);

            // Create subscription if auto_renewal is enabled
            if ($data['auto_renewal'] && !empty($data['custom_subscription_fee'])) {
                $subscriptionOptions = [
                    'monthly_amount' => $data['custom_subscription_fee'],
                    'start_date' => $data['contract']['start_date'] ?? now(),
                    'billing_cycle' => 'monthly',
                    'auto_renewal' => true,
                    'notes' => "Auto-created from contract: {$contract->unique_id}",
                ];

                $subscription = $this->subscriptionService->createFromContract($contract, $subscriptionOptions);

                // Load subscription relationship for response
                $contract->load('subscription');
            }

            // Notify admins about the new contract (if it's from a visitor)
            if (auth()->user()->hasRole('visitor')) {
                $this->contractService->notifyAdminsAboutContractRequest($contract);
            }

            $message = 'Contract created successfully';
            $message_kr = '계약이 성공적으로 생성되었습니다.';

            if ($data['auto_renewal']) {
                $message .= ' with subscription enabled';
                $message_kr .= ' (구독 활성화됨)';
            }

            return $this->apiResponse(
                new DetailsResource($contract->load(['developmentItems', 'subscription'])),
                $message,
                $message_kr,
                true,
                201
            );
        } catch (\Exception $e) {
            $message = 'Error creating contract';
            $message_kr = '계약 생성 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    // Store multiple contracts
    public function storeMultiple(ContractMultiStoreRequest $request)
    {
        try {
            $validatedData = $request->validatedData();

            $this->contractService->storeMultiple($validatedData);

            $message = 'Contracts created successfully';
            $message_kr = '계약이 성공적으로 생성되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating contracts';
            $message_kr = '계약 생성 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }

    }
    public function update(ContractUpdateRequest $request, $id)
    {
        // try {
            $data = $request->validatedData();
            $updatedContract = $this->contractService->update($id, $data['contract']);

            // return $data['development_items'];
            // Handle files - only process new uploads
            if (!empty($data['rfp_files'])) {
                $filesToInsert = array_filter($data['rfp_files'], function($file) {
                    // Only process strings that don't look like paths (new uploads)
                    return !is_string($file) || !file_exists(public_path($file));
                });
                if (!empty($filesToInsert)) {
                    $this->contractService->insertFiles($updatedContract, $filesToInsert);
                }
            }

            // Handle images - only process new uploads
            if (!empty($data['rfp_images'])) {
                $imagesToInsert = array_filter($data['rfp_images'], function($image) {
                    // Only process strings that don't look like paths (new uploads)
                    return !is_string($image) || !file_exists(public_path($image));
                });
                if (!empty($imagesToInsert)) {
                    $this->contractService->insertImages($updatedContract, $imagesToInsert);
                }
            }

            // Add development items if any
            if (!empty($data['development_items'])) {
                $this->contractService->removeDevelopmentItems($updatedContract);
                $this->contractService->insertDevelopmentItems($updatedContract, $data['development_items']);
            }

            // Load relationships for the contract
            $updatedContract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments',
                'parentContract',
                'childContracts'
            ]);

            $message = 'Contract updated successfully';
            $message_kr = '계약이 성공적으로 업데이트되었습니다.';
            return $this->apiResponse(new DetailsResource($updatedContract), $message, $message_kr, true, 200);
        // } catch (\Exception $e) {
        //     $message = 'Error updating contract';
        //     $message_kr = '계약 업데이트 중 오류가 발생했습니다.';
        //     return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        // }
    }

    // Delete a contract
    public function destroy($id)
    {
        try {
            $this->contractService->delete($id);
            $message = 'Contract deleted successfully';
            $message_kr = '계약이 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting contract';
            $message_kr = '계약 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    public function deleteRfpFile($id) {
        try {
            $this->contractService->deleteRfpFile($id);
            $message = 'RFP File deleted successfully';
            $message_kr = 'RFP 파일이 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting file';
            $message_kr = '파일 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }

    public function deleteRfpImage($id) {
        try {
            $this->contractService->deleteRfpImage($id);
            $message = 'RFP Image deleted successfully';
            $message_kr = 'RFP 이미지가 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting image';
            $message_kr = '이미지 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
    }


    public function acceptContract(ContractAcceptRequest $request, $id)
    {
        try {
            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if the current user is the customer of this contract
            if (auth()->id() != $contract->customer_id) {
                $message = 'You are not authorized to accept this contract';
                $message_kr = '이 계약을 수락할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Get validated data
            $data = $request->validatedData();

            // Update contract status to "Accepted" and set acceptance_date
            $contract->status = 'Accepted';

            // Update payment status if payment information is provided
            if (!empty($data['payment']) && !empty($data['payment']['paid_amount'])) {
                $paidAmount = $data['payment']['paid_amount'];

                if ($paidAmount >= $contract->amount) {
                    $contract->payment_status = 'Paid';
                } elseif ($paidAmount > 0) {
                    $contract->payment_status = 'Partial';
                }
            }

            $contract->save();

            // Create a status entry for the acceptance
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Accepted',
                'comment' => $data['comment'] ?? 'Contract accepted by customer',
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Add files if any
            if (!empty($data['files'])) {
                foreach ($data['files'] as $file) {
                    try {

                        // Also add file to status files
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $file['path'],
                            'original_filename' => $file['original_name'],
                            'file_type' => $file['type'],
                            'file_size' => $file['size'],
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in acceptContract: ' . $e->getMessage());
                    }
                }
            }

            // Create payment record if payment information is provided
            if (!empty($data['payment']) && !empty($data['payment']['paid_amount'])) {
                $paymentData = [
                    'contact_id' => $contract->id,
                    'agreed_payment' => $contract->amount,
                    'paid_amount' => $data['payment']['paid_amount'],
                    'payment_date' => $data['payment']['payment_date'] ?? now(),
                    'payment_type' => $data['payment']['payment_type'] ?? null,
                    'remarks' => $data['payment']['remarks'] ?? null,
                    'payment_gateway_id' => $data['payment']['payment_gateway_id'] ?? null,
                    'payment_gateway_method_id' => $data['payment']['payment_gateway_method_id'] ?? null,
                    'payment_gateway_name' => $data['payment']['payment_gateway_name'] ?? null,
                    'payment_gateway_method_name' => $data['payment']['payment_gateway_method_name'] ?? null,
                ];

                // Determine payment status
                if ($paymentData['paid_amount'] >= $paymentData['agreed_payment']) {
                    $paymentData['status'] = 'complete';
                } elseif ($paymentData['paid_amount'] > 0) {
                    $paymentData['status'] = 'partial';
                } else {
                    $paymentData['status'] = 'pending';
                }
                // return $paymentData;
                // Create the payment record
                $payment = new \App\Models\Payment($paymentData);
                $payment->save();
            }

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments',
                'parentContract',
                'childContracts'
            ]);

            // Notify all admin users about the accepted contract
            $this->contractService->notifyAdminsAboutAcceptedContract($contract);

            $message = 'Contract accepted successfully';
            $message_kr = '계약이 성공적으로 수락되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error accepting contract';
            $message_kr = '계약 수락 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Create a new contract request as a visitor
     *
     * @param VisitorContractRequestStoreRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createVisitorRequest(VisitorContractRequestStoreRequest $request)
    {
        try {
            $data = $request->validatedData();

            // Create the contract
            $contract = $this->contractService->store($data['contract']);

            // Add files and images if any
            if (!empty($data['rfp_files'])) {
                $this->contractService->insertFiles($contract, $data['rfp_files']);
            }

            if (!empty($data['rfp_images'])) {
                $this->contractService->insertImages($contract, $data['rfp_images']);
            }

            // Create initial status entry
            \App\Models\PenguinContractStatus::create([
                'contract_id' => $contract->id,
                'status' => 'Pending',
                'comment' => 'New contract request from visitor',
                'user_id' => auth()->id(),
            ]);

            // Notify admins about the new contract request
            $this->contractService->notifyAdminsAboutContractRequest($contract);

            $message = 'Contract request created successfully';
            $message_kr = '계약 요청이 성공적으로 생성되었습니다';

            return $this->apiResponse(
                new DetailsResource($contract),
                $message,
                $message_kr,
                true,
                201
            );
        } catch (\Exception $e) {
            $message = 'Error creating contract request';
            $message_kr = '계약 요청 생성 중 오류가 발생했습니다';

            return $this->apiResponse(
                [],
                $e->getMessage(),
                $message_kr,
                false,
                500
            );
        }
    }

    /**
     * Create a new visitor contract with a parent contract ID and Pending status
     *
     * @param VisitorContractStoreRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createVisitorContract(VisitorContractStoreRequest $request)
    {
        try {
            $data = $request->validatedData();

            // Create the contract
            $contract = $this->contractService->store($data['contract']);

            // Add files and images if any
            if (!empty($data['rfp_files'])) {
                $this->contractService->insertFiles($contract, $data['rfp_files']);
            }

            if (!empty($data['rfp_images'])) {
                $this->contractService->insertImages($contract, $data['rfp_images']);
            }

            // Add development items if any
            if (!empty($data['development_items'])) {
                $this->contractService->insertDevelopmentItems($contract, $data['development_items']);
            }
            // Create initial status entry
            \App\Models\PenguinContractStatus::create([
                'contract_id' => $contract->id,
                'status' => 'Pending',
                'comment' => 'New visitor contract with parent contract',
                'user_id' => auth()->id(),
            ]);

            // Notify admins about the new contract
            $this->contractService->notifyAdminsAboutContractRequest($contract);

            $message = 'Visitor contract created successfully';
            $message_kr = '방문자 계약이 성공적으로 생성되었습니다';

            return $this->apiResponse(
                new DetailsResource($contract),
                $message,
                $message_kr,
                true,
                201
            );
        } catch (\Exception $e) {
            $message = 'Error creating visitor contract';
            $message_kr = '방문자 계약 생성 중 오류가 발생했습니다';

            return $this->apiResponse(
                [],
                $e->getMessage(),
                $message_kr,
                false,
                500
            );
        }
    }

    public function addStatusRemark(Request $request, $id)
    {
        try {
            $request->validate([
                'comment' => 'required|string|max:500',
                'files' => 'nullable|array',
                'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
            ]);

            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Create a new status entry with the same status but with a comment
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => $contract->status,
                'comment' => $request->comment,
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Process files if uploaded using S3
            if ($request->hasFile('files')) {
                $fileUploadHelper = new FileUploadHelper();
                $files = $fileUploadHelper->processFileUploads($request, 'files', 'contract_status');

                foreach ($files as $fileData) {
                    // Create a new file entry
                    \App\Models\PenguinContractStatusFile::create([
                        'status_id' => $statusEntry->id,
                        'file_path' => $fileData['path'],
                        'original_filename' => $fileData['original_name'],
                        'file_type' => $fileData['type'],
                        'file_size' => $fileData['size'],
                    ]);
                }
            }

            $message = 'Status comment added successfully';
            $message_kr = '상태 코멘트가 성공적으로 추가되었습니다.';

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error adding status comment';
            $message_kr = '상태 코멘트 추가 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Approve a contract with payment information
     *
     * @param ContractApproveRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveContract(ContractApproveRequest $request, $id)
    {
        try {
            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if the contract is in 'Accepted' status
            if ($contract->status !== 'Accepted') {
                $message = 'Only contracts with Accepted status can be approved';
                $message_kr = '수락된 상태의 계약만 승인할 수 있습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            // Get validated data
            $data = $request->validatedData();

            // Update contract status to "Approved"
            $contract->status = 'Approved';

            // Update payment status if payment information is provided
            if (!empty($data['payment']) && !empty($data['payment']['paid_amount'])) {
                $paidAmount = $data['payment']['paid_amount'];

                if ($paidAmount >= $contract->amount) {
                    $contract->payment_status = 'Paid';
                } elseif ($paidAmount > 0) {
                    $contract->payment_status = 'Partial';
                }
            }

            $contract->save();

            // Create a status entry for the approval
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Approved',
                'comment' => $data['note'],
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Add files if any
            if (!empty($data['files'])) {
                foreach ($data['files'] as $file) {
                    try {
                        // Add file to status files
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $file['path'],
                            'original_filename' => $file['original_name'],
                            'file_type' => $file['type'],
                            'file_size' => $file['size'],
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in approveContract: ' . $e->getMessage());
                    }
                }
            }

            // Create payment record if payment information is provided
            if (!empty($data['payment']) && !empty($data['payment']['paid_amount'])) {
                $paymentData = [
                    'contact_id' => $contract->id,
                    'agreed_payment' => $contract->amount,
                    'paid_amount' => $data['payment']['paid_amount'],
                    'payment_date' => $data['payment']['payment_date'] ?? now(),
                    'payment_type' => $data['payment']['payment_type'] ?? null,
                    'remarks' => $data['payment']['remarks'] ?? null,
                    'payment_gateway_id' => $data['payment']['payment_gateway_id'] ?? null,
                    'payment_gateway_method_id' => $data['payment']['payment_gateway_method_id'] ?? null,
                    'payment_gateway_name' => $data['payment']['payment_gateway_name'] ?? null,
                    'payment_gateway_method_name' => $data['payment']['payment_gateway_method_name'] ?? null,
                ];

                // Determine payment status
                if ($paymentData['paid_amount'] >= $paymentData['agreed_payment']) {
                    $paymentData['status'] = 'complete';
                } elseif ($paymentData['paid_amount'] > 0) {
                    $paymentData['status'] = 'partial';
                } else {
                    $paymentData['status'] = 'pending';
                }

                // Create the payment record
                $payment = \App\Models\Payment::where('contact_id', $contract->id)->first();
                if ($payment) {
                    $payment->update($paymentData);
                } else {
                    $payment = new \App\Models\Payment($paymentData);
                    $payment->save();
                }
            }

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            // Notify the customer about payment approval
            $this->contractService->notifyCustomerAboutPaymentApproval($contract);

            $message = 'Contract approved successfully';
            $message_kr = '계약이 성공적으로 승인되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error approving contract';
            $message_kr = '계약 승인 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }


    public function sendToDevelopment(Request $request, $id)
    {
        try {
            // Validate request
            $request->validate([
                'comment' => 'nullable|string|max:500',
                'files' => 'nullable|array',
                'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
            ]);

            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if the contract is in 'Approved' status
            if ($contract->status !== 'Approved') {
                $message = 'Only contracts with Approved status can be sent to development';
                $message_kr = '승인된 상태의 계약만 개발팀에 전달할 수 있습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            // Check if user is admin
            if (!auth()->user()->hasRole('admin')) {
                $message = 'You are not authorized to send this contract to development';
                $message_kr = '이 계약을 개발팀에 전달할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Update contract status to "Development"
            $contract->status = 'Development';
            $contract->save();

            // Create a status entry for the development
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Development',
                'comment' => $request->comment,
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Process files if uploaded
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    try {
                        // Get file information before moving it
                        $originalName = $file->getClientOriginalName();
                        $mimeType = $file->getClientMimeType();
                        $size = $file->getSize();

                        // Generate filename and move file
                        $filename = time() . '_' . $originalName;
                        $destinationPath = public_path('uploads/contract_status');

                        // Create directory if it doesn't exist
                        if (!file_exists($destinationPath)) {
                            mkdir($destinationPath, 0755, true);
                        }

                        $file->move($destinationPath, $filename);
                        $filePath = 'contract_status/' . $filename;

                        // Create a new file entry
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $filePath,
                            'original_filename' => $originalName,
                            'file_type' => $mimeType,
                            'file_size' => $size,
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in sendToDevelopment: ' . $e->getMessage());
                    }
                }
            }

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            // Notify all bacbon users about the contract being sent to development
            $this->contractService->notifyBacbonUsersAboutDevelopment($contract, $request->comment);

            $message = 'Contract sent to development successfully';
            $message_kr = '계약이 성공적으로 개발팀에 전달되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error sending contract to development';
            $message_kr = '계약을 개발팀에 전달하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }


    public function getBackbonContracts()
    {
        try {
            // Check if user has bacbon role
            if (!auth()->user()->hasRole('bacbon')) {
                $message = 'You are not authorized to access this resource';
                $message_kr = '이 리소스에 접근할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Get contracts with specific statuses
            $contracts = $this->contractService->getContractsByStatuses([
                'Development',
                'Maintenance',
                'Completed',
                'Rejected'
            ]);

            $message = 'Contracts retrieved successfully';
            $message_kr = '계약이 성공적으로 검색되었습니다.';

            return $this->apiResponse($contracts, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contracts';
            $message_kr = '계약을 검색하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    public function adminCompleteContract(AdminCompleteContractRequest $request, $id)
    {
        try {
            // Get validated data
            $data = $request->validatedData();

            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if user has admin role
            if (!auth()->user()->hasRole('admin')) {
                $message = 'You are not authorized to complete this contract';
                $message_kr = '이 계약을 완료할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Update contract status to "Completed" and set completion_date
            $contract->status = 'Completed';
            $contract->save();

            // Create a status entry for the completion
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Completed',
                'comment' => $data['comment'],
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Process files if uploaded
            if (!empty($data['files'])) {
                foreach ($data['files'] as $file) {
                    try {
                        // Create a new file entry
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $file['path'],
                            'original_filename' => $file['original_name'],
                            'file_type' => $file['type'],
                            'file_size' => $file['size'],
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in adminCompleteContract: ' . $e->getMessage());
                    }
                }
            }

            // Notify visitor and bacbon users about the completed contract
            $this->contractService->notifyUsersAboutAdminCompletedContract($contract, $data['comment']);

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            $message = 'Contract marked as completed successfully';
            $message_kr = '계약이 성공적으로 완료되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error marking contract as completed';
            $message_kr = '계약을 완료로 표시하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    public function inProgressContract(Request $request, $id)
    {
        try {
            // Validate request
            $request->validate([
                'comment' => 'required|string|max:500',
                'files' => 'nullable|array',
                'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
            ]);

            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if user has bacbon role
            if (!auth()->user()->hasRole('bacbon')) {
                $message = 'You are not authorized to update this contract';
                $message_kr = '이 계약을 업데이트할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Check if the contract is in 'Development' status
            if ($contract->status !== 'Development') {
                $message = 'Only contracts with Development status can be updated to In Progress';
                $message_kr = '개발 상태의 계약만 진행 중으로 업데이트할 수 있습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            // Update contract status to "In Progress"
            $contract->status = 'Maintenance';
            $contract->save();

            // Create a status entry for the status update
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Maintenance',
                'comment' => $request->comment,
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Process files if uploaded
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    try {
                        // Get file information before moving it
                        $originalName = $file->getClientOriginalName();
                        $mimeType = $file->getClientMimeType();
                        $size = $file->getSize();

                        // Generate filename and move file
                        $filename = time() . '_' . $originalName;
                        $destinationPath = public_path('uploads/contract_status');

                        // Create directory if it doesn't exist
                        if (!file_exists($destinationPath)) {
                            mkdir($destinationPath, 0755, true);
                        }

                        $file->move($destinationPath, $filename);
                        $filePath = 'contract_status/' . $filename;

                        // Create a new file entry
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $filePath,
                            'original_filename' => $originalName,
                            'file_type' => $mimeType,
                            'file_size' => $size,
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in inProgressContract: ' . $e->getMessage());
                    }
                }
            }

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            // Notify customer and admin users about the contract being moved to in-progress
            $this->contractService->notifyUsersAboutInProgressContract($contract, $request->comment);

            $message = 'Contract status updated to In Progress';
            $message_kr = '계약 상태가 진행 중으로 업데이트되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating contract status';
            $message_kr = '계약 상태 업데이트 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Mark a contract as completed
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function makeDoneContract(Request $request, $id)
    {
        try {
            // Validate request
            $request->validate([
                'comment' => 'required|string|max:500',
                'files' => 'nullable|array',
                'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
            ]);

            // Find the contract
            $contract = $this->contractService->find($id);

            if (!$contract) {
                $message = 'Contract not found';
                $message_kr = '계약을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }

            // Check if user has bacbon role
            if (!auth()->user()->hasRole('bacbon')) {
                $message = 'You are not authorized to update this contract';
                $message_kr = '이 계약을 업데이트할 권한이 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 403);
            }

            // Check if the contract is in 'In Progress' status
            if ($contract->status !== 'Maintenance') {
                $message = 'Only contracts with In Progress status can be marked as completed';
                $message_kr = '진행 중인 상태의 계약만 완료로 표시할 수 있습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 422);
            }

            // Update contract status to "Completed" and set completion date
            $contract->status = 'Developed';
            $contract->save();

            // Create a status entry for the completion
            $statusEntry = new \App\Models\PenguinContractStatus([
                'contract_id' => $contract->id,
                'status' => 'Developed',
                'comment' => $request->comment,
                'user_id' => auth()->id(),
            ]);
            $statusEntry->save();

            // Process files if uploaded
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    try {
                        // Get file information before moving it
                        $originalName = $file->getClientOriginalName();
                        $mimeType = $file->getClientMimeType();
                        $size = $file->getSize();

                        // Generate filename and move file
                        $filename = time() . '_' . $originalName;
                        $destinationPath = public_path('uploads/contract_completion');

                        // Create directory if it doesn't exist
                        if (!file_exists($destinationPath)) {
                            mkdir($destinationPath, 0755, true);
                        }

                        $file->move($destinationPath, $filename);
                        $filePath = 'contract_completion/' . $filename;

                        // Create a new file entry
                        \App\Models\PenguinContractStatusFile::create([
                            'status_id' => $statusEntry->id,
                            'file_path' => $filePath,
                            'original_filename' => $originalName,
                            'file_type' => $mimeType,
                            'file_size' => $size,
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but continue processing other files
                        \Log::error('Error processing file in makeDoneContract: ' . $e->getMessage());
                    }
                }
            }

            // Notify all admin users about the completed contract
            $this->contractService->notifyAdminsAboutCompletedContract($contract);

            // Load the contract with its relationships
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'images',
                'files',
                'payments'
            ]);

            $message = 'Contract marked as completed successfully';
            $message_kr = '계약이 성공적으로 완료되었습니다.';

            return $this->apiResponse(new DetailsResource($contract), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error marking contract as completed';
            $message_kr = '계약을 완료로 표시하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }


}

