<?php

namespace App\Http\Controllers;

use App\Http\Requests\SupportRequestStoreRequest;
use App\Http\Requests\SupportRequestUpdateRequest;
use App\Http\Requests\SupportRequest\ConvertToContractRequest;
use App\Models\Contract;
use App\Models\SupportRequest;
use App\Models\SupportRequestFile;
use App\Models\User;
use App\Notifications\SupportRequestConvertedToContract;
use App\Services\ContractService;
use App\Services\SupportRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class SupportRequestController extends Controller
{
    private SupportRequestService $service;
    private ContractService $contractService;

    public function __construct(SupportRequestService $service, ContractService $contractService)
    {
        $this->service = $service;
        $this->contractService = $contractService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $supportRequests = $this->service->all();
            $message = 'Support requests retrieved successfully.';
            $message_kr = '지원 요청이 성공적으로 검색되었습니다.';
            return $this->apiResponse($supportRequests, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving support requests: ' . $e->getMessage();
            $message_kr = '지원 요청을 검색하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SupportRequestStoreRequest $request)
    {
        try {
            
            $developmentItems = $request->input('development_items') ?? [];
            if (is_string($developmentItems)) {
                $developmentItems = json_decode($developmentItems, true);
            }

            // return $developmentItems;

            $data = $request->validatedData();

            // Log the validated data for debugging
            \Log::info('Support request validated data:', [
                'data' => $data,
                'files_count' => count($data['files']),
                'development_items_count' => count($data['development_items'])
            ]);

            $supportRequest = $this->service->store($data, $developmentItems);

            $message = 'Support request submitted successfully.';
            $message_kr = '지원 요청이 성공적으로 제출되었습니다.';

            return $this->apiResponse($supportRequest, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Error submitting support request: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            $message = 'Error submitting support request: ' . $e->getMessage();
            $message_kr = '지원 요청을 제출하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $supportRequest = $this->service->find($id);

            if (!$supportRequest) {
                $message = 'Support request not found.';
                $message_kr = '지원 요청을 찾을 수 없습니다.';
                return $this->apiResponse(null, $message, $message_kr, false, 404);
            }

            $message = 'Support request retrieved successfully.';
            $message_kr = '지원 요청이 성공적으로 검색되었습니다.';

            return $this->apiResponse($supportRequest, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving support request: ' . $e->getMessage();
            $message_kr = '지원 요청을 검색하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SupportRequestUpdateRequest $request, $id)
    {
        try {
            $supportRequest = SupportRequest::findOrFail($id);

            // Check if user can access this request
            if (!$this->service->canAccess($supportRequest)) {
                $message = 'You are not authorized to update this support request.';
                $message_kr = '이 지원 요청을 업데이트할 권한이 없습니다.';
                return $this->apiResponse(null, $message, $message_kr, false, 403);
            }

            $data = $request->validatedData();
            $updatedRequest = $this->service->update($supportRequest, $data);

            $message = 'Support request updated successfully.';
            $message_kr = '지원 요청이 성공적으로 업데이트되었습니다.';

            return $this->apiResponse($updatedRequest, $message, $message_kr, true, 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            $message = 'Support request not found.';
            $message_kr = '지원 요청을 찾을 수 없습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 404);
        } catch (\Exception $e) {
            $message = 'Error updating support request: ' . $e->getMessage();
            $message_kr = '지원 요청을 업데이트하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $supportRequest = SupportRequest::findOrFail($id);

            // Check if user can access this request
            if (!$this->service->canAccess($supportRequest)) {
                $message = 'You are not authorized to delete this support request.';
                $message_kr = '이 지원 요청을 삭제할 권한이 없습니다.';
                return $this->apiResponse(null, $message, $message_kr, false, 403);
            }

            $this->service->delete($supportRequest);

            $message = 'Support request deleted successfully.';
            $message_kr = '지원 요청이 성공적으로 삭제되었습니다.';

            return $this->apiResponse(null, $message, $message_kr, true, 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            $message = 'Support request not found.';
            $message_kr = '지원 요청을 찾을 수 없습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 404);
        } catch (\Exception $e) {
            $message = 'Error deleting support request: ' . $e->getMessage();
            $message_kr = '지원 요청을 삭제하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Delete a specific file from a support request.
     */
    public function deleteFile($id)
    {
        try {
            $file = SupportRequestFile::findOrFail($id);
            $supportRequest = $file->supportRequest;

            // Check if user can access this request
            if (!$this->service->canAccess($supportRequest)) {
                $message = 'You are not authorized to delete files from this support request.';
                $message_kr = '이 지원 요청에서 파일을 삭제할 권한이 없습니다.';
                return $this->apiResponse(null, $message, $message_kr, false, 403);
            }

            $this->service->deleteFile($file);

            $message = 'File deleted successfully.';
            $message_kr = '파일이 성공적으로 삭제되었습니다.';

            return $this->apiResponse(null, $message, $message_kr, true, 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            $message = 'File not found.';
            $message_kr = '파일을 찾을 수 없습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 404);
        } catch (\Exception $e) {
            $message = 'Error deleting file: ' . $e->getMessage();
            $message_kr = '파일을 삭제하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Find support requests by email.
     */
    public function findByEmail(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
            ]);

            $supportRequests = $this->service->findByEmail($request->email);

            $message = 'Support requests retrieved successfully.';
            $message_kr = '지원 요청이 성공적으로 검색되었습니다.';

            return $this->apiResponse($supportRequests, $message, $message_kr, true, 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $message = 'Invalid email address.';
            $message_kr = '유효하지 않은 이메일 주소입니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 422);
        } catch (\Exception $e) {
            $message = 'Error retrieving support requests: ' . $e->getMessage();
            $message_kr = '지원 요청을 검색하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }


    /**
     * Convert a support request to a contract.
     *
     * @param ConvertToContractRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function convertToContracts(ConvertToContractRequest $request)
    {
        try {
            // Get validated data
            $data = $request->validatedData();

            // Find the support request
            $supportRequest = SupportRequest::findOrFail($data['support_request_id']);

            // Create contract data
            $contractData = [
                'customer_id' => $supportRequest->customer_id,
                'contract_name' => $supportRequest->title,
                'contract_details' => $supportRequest->description,
                'amount' => $data['amount'],
                'start_date' => $data['start_date'] ?? now(),
                'status' => 'New Lead',
                'base_construction_cost' => $supportRequest->base_construction_cost,
                'monthly_subscription_fee' => $supportRequest->monthly_subscription_fee,
                'additional_items_total' => $supportRequest->additional_items_total,
                'total_construction_cost' => $supportRequest->total_construction_cost,
            ];

            // If support request is from a registered user, use their ID as customer_id
            if ($supportRequest->user_id) {
                $contractData['customer_id'] = $supportRequest->user_id;
            }

            // Generate a unique ID for the contract
            $contractData['unique_id'] = $this->contractService->generateUniqueId();

            // Create the contract
            $contract = Contract::create($contractData);

            // Create initial status entry
            $statusEntry = \App\Models\PenguinContractStatus::create([
                'contract_id' => $contract->id,
                'status' => $contract->status,
                'comment' => 'Contract created from support request #' . $supportRequest->id,
                'user_id' => auth()->id(),
            ]);

            // Copy support request files to contract files
            if ($supportRequest->files && $supportRequest->files->count() > 0) {
                foreach ($supportRequest->files as $file) {
                    // Add file to RfpFile for the contract
                    \App\Models\RfpFile::create([
                        'contract_id' => $contract->id,
                        'file' => $file->file_path,
                    ]);
                }
            }

            // Copy development items to contract development items
            if ($supportRequest->developmentItems && $supportRequest->developmentItems->count() > 0) {
                foreach ($supportRequest->developmentItems as $item) {
                    \App\Models\ContractDevelopmentItem::create([
                        'contract_id' => $contract->id,
                        'development_item_id' => $item->development_item_id,
                        'name' => $item->name,
                        'price' => $item->price,
                        'quantity' => $item->quantity,
                        'total' => $item->total,
                    ]);
                }
            }

            // Update support request status to "In Progress"
            $supportRequest->status = 'In Progress';
            $supportRequest->save();

            // Send notification to the customer
            if ($supportRequest->user_id) {
                // If it's a registered user
                $user = User::find($supportRequest->user_id);
                if ($user) {
                    Notification::send($user, new SupportRequestConvertedToContract($contract, $supportRequest));
                }
            } else if ($supportRequest->email) {
                // If it's a guest user with email
                // Create a temporary user object for notification
                $tempUser = new \stdClass();
                $tempUser->email = $supportRequest->email;
                $tempUser->name = $supportRequest->name ?? 'Customer';

                Notification::route('mail', $supportRequest->email)
                    ->notify(new SupportRequestConvertedToContract($contract, $supportRequest));
            }

            // Load relationships for the contract
            $contract->load([
                'statusHistory.user',
                'statusHistory.files',
                'customer:id,name,username,email',
                'creator:id,name,username,email',
                'files',
                'images',
                'developmentItems'
            ]);

            $message = 'Support request converted to contract successfully.';
            $message_kr = '지원 요청이 계약으로 성공적으로 변환되었습니다.';

            return $this->apiResponse($contract, $message, $message_kr, true, 201);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            $message = 'Support request not found.';
            $message_kr = '지원 요청을 찾을 수 없습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 404);
        } catch (\Exception $e) {
            $message = 'Error converting support request to contract: ' . $e->getMessage();
            $message_kr = '지원 요청을 계약으로 변환하는 중 오류가 발생했습니다.';

            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }
}
