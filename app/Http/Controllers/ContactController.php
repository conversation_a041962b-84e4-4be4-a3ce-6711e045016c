<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Services\ContactService;
use Illuminate\Http\Request;

use App\Http\Requests\Contact\ContactStoreRequest;
use App\Http\Requests\Contact\ContactUpdateRequest;
use App\Http\Requests\Contact\ContactMultiStoreRequest;

use App\Http\Resources\Contract\DetailsResource;
use G4T\Swagger\Attributes\SwaggerSection;
 
#[SwaggerSection('everything about your contracts')]
class ContactController extends Controller
{
    private ContactService $contactService;

    public function __construct(ContactService $contactService)
    {
        $this->contactService = $contactService;
    }

    // Get all contacts
    public function index(Request $request)
    {
        try {
            $contacts = $this->contactService->all();
            
            $message = 'Contracts retrieved successfully';
            $message_kr = '연락처가 성공적으로 검색되었습니다'; 
        
            return $this->apiResponse($contacts, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            
            $message = 'Error retrieving contacts';
            $message_kr = '연락처를 검색하는 중 오류가 발생했습니다';
        
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
        
    }

    // Find a specific contact by ID
    public function show($id)
    {
        try {
            $contact = $this->contactService->find($id);
        
            if (!$contact) {
                $message = 'Contract not found';
                $message_kr = '연락처를 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }
        
            $message = 'Contract retrieved successfully';
            $message_kr = '연락처가 성공적으로 조회되었습니다.';
            return $this->apiResponse(new DetailsResource($contact), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving contract';
            $message_kr = '연락처를 조회하는 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
        
    }

    // Store a single contact
    public function store(ContactStoreRequest $request)
    {
        try {
            $validatedData = $request->validatedData();
        
            $contact = $this->contactService->store($validatedData);
        
            $message = 'Contract created successfully';
            $message_kr = '연락처가 성공적으로 생성되었습니다.';
            return $this->apiResponse($contact, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating contract';
            $message_kr = '연락처 생성 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }        
    }

    // Store multiple contacts
    public function storeMultiple(ContactMultiStoreRequest $request)
    {
        try {
            $validatedData = $request->validatedData(); 
        
            $this->contactService->storeMultiple($validatedData);
        
            $message = 'Contracts created successfully';
            $message_kr = '연락처가 성공적으로 생성되었습니다.';  
            return $this->apiResponse([], $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Error creating contacts';
            $message_kr = '연락처 생성 중 오류가 발생했습니다.';  
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }
        
    }

    // Update a contact
    public function update(ContactUpdateRequest $request, $id)
    {
        try {
            $data = $request->validatedData();
            $updatedContact = $this->contactService->update($id, $data['contract']);
            $this->contactService->insertFiles($updatedContact, $data['rfp_files']);
            $this->contactService->insertImages($updatedContact, $data['rfp_images']);
        
            $message = 'Contract updated successfully';
            $message_kr = '연락처가 성공적으로 업데이트되었습니다.';
            return $this->apiResponse(new DetailsResource($updatedContact), $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating contract';
            $message_kr = '연락처 업데이트 중 오류가 발생했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        } 
    }

    // Delete a contact
    public function destroy($id)
    {
        try {
            $this->contactService->delete($id);
            $message = 'Contract deleted successfully';
            $message_kr = '연락처가 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting contract';
            $message_kr = '연락처 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }        
    }

    public function deleteRfpFile($id) {
        try {
            $this->contactService->deleteRfpFile($id);
            $message = 'RFP File deleted successfully';
            $message_kr = 'RFP 파일이 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting file';
            $message_kr = '연락처 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }  
    }

    public function deleteRfpImage($id) {
        try {
            $this->contactService->deleteRfpImage($id);
            $message = 'RFP Image deleted successfully';
            $message_kr = 'RFP 이미지가 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error deleting image';
            $message_kr = '연락처 삭제 중 오류가 발생했습니다.';
            return $this->apiResponse([], $message, $message_kr, false, 500);
        }  
    }
}


