<?php

namespace App\Http\Controllers;

use App\Http\Requests\PaymentGatewayMethodStoreRequest;
use App\Http\Requests\PaymentGatewayMethodUpdateRequest;
use App\Services\PaymentGatewayMethodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentGatewayMethodController extends Controller
{
    private PaymentGatewayMethodService $service;

    public function __construct(PaymentGatewayMethodService $service)
    {
        $this->service = $service;
    }

    /**
     * Get all methods for a specific payment gateway.
     *
     * @param int $paymentGatewayId
     * @return JsonResponse
     */
    public function getByPaymentGatewayId($paymentGatewayId): JsonResponse
    {
        try {
            $methods = $this->service->getByPaymentGatewayId($paymentGatewayId);
            $message = 'Payment gateway methods retrieved successfully.';
            $message_kr = '결제 게이트웨이 방법이 성공적으로 검색되었습니다.';
            return $this->apiResponse($methods, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to retrieve payment gateway methods.';
            $message_kr = '결제 게이트웨이 방법을 검색하지 못했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Store a newly created payment gateway method.
     *
     * @param PaymentGatewayMethodStoreRequest $request
     * @return JsonResponse
     */
    public function store(PaymentGatewayMethodStoreRequest $request): JsonResponse
    {
        try {
            $data = $request->validatedData();
            $method = $this->service->store($data);
            $message = 'Payment gateway method created successfully.';
            $message_kr = '결제 게이트웨이 방법이 성공적으로 생성되었습니다.';
            return $this->apiResponse($method, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = 'Failed to create payment gateway method.';
            $message_kr = '결제 게이트웨이 방법을 생성하지 못했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Display the specified payment gateway method.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $method = $this->service->find($id);
            if (!$method) {
                $message = 'Payment gateway method not found.';
                $message_kr = '결제 게이트웨이 방법을 찾을 수 없습니다.';
                return $this->apiResponse([], $message, $message_kr, false, 404);
            }
            $message = 'Payment gateway method retrieved successfully.';
            $message_kr = '결제 게이트웨이 방법이 성공적으로 검색되었습니다.';
            return $this->apiResponse($method, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to retrieve payment gateway method.';
            $message_kr = '결제 게이트웨이 방법을 검색하지 못했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Update the specified payment gateway method.
     *
     * @param PaymentGatewayMethodUpdateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(PaymentGatewayMethodUpdateRequest $request, $id): JsonResponse
    {
        try {
            $data = $request->validatedData();
            $method = $this->service->update($id, $data);
            $message = 'Payment gateway method updated successfully.';
            $message_kr = '결제 게이트웨이 방법이 성공적으로 업데이트되었습니다.';
            return $this->apiResponse($method, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to update payment gateway method.';
            $message_kr = '결제 게이트웨이 방법을 업데이트하지 못했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }

    /**
     * Remove the specified payment gateway method.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $this->service->delete($id);
            $message = 'Payment gateway method deleted successfully.';
            $message_kr = '결제 게이트웨이 방법이 성공적으로 삭제되었습니다.';
            return $this->apiResponse([], $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Failed to delete payment gateway method.';
            $message_kr = '결제 게이트웨이 방법을 삭제하지 못했습니다.';
            return $this->apiResponse([], $e->getMessage(), $message_kr, false, 500);
        }
    }
}
