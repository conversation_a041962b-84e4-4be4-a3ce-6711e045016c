<?php

namespace App\Http\Controllers;

use App\Models\DevelopmentRequest;
use App\Services\DevelopmentRequestService;
use Illuminate\Http\Request;
use App\Http\Requests\DevelopmentRequestStoreRequest;
use App\Http\Requests\DevelopmentUpdateRequest;

class DevelopmentRequestController extends Controller
{
    private DevelopmentRequestService $service;

    public function __construct(DevelopmentRequestService $service)
    {
        $this->service = $service;
    }


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $developmentRequests = $this->service->all();
            $message = 'Development requests retrieved successfully.';
            $message_kr = '개발 요청이 성공적으로 검색되었습니다.';
            return $this->apiResponse($developmentRequests, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving development requests.';
            $message_kr = '개발 요청을 검색하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

 
    /**
     * Store a newly created resource in storage.
     */
    public function store(DevelopmentRequestStoreRequest $request)
    {
        try {
            $data = $request->validatedData();
            $developmentRequest = $this->service->store($data);
            $message = 'Development request created successfully.';
            $message_kr = '개발 요청이 성공적으로 생성되었습니다.';
            return $this->apiResponse($developmentRequest, $message, $message_kr, true, 201);
        } catch (\Exception $e) {
            $message = $e->getMessage();
            $message_kr = '개발 요청을 생성하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(DevelopmentRequest $developmentRequest)
    {
        try {
            $message = 'Development request retrieved successfully.';
            $message_kr = '개발 요청이 성공적으로 검색되었습니다.';
            return $this->apiResponse($developmentRequest, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error retrieving development request.';
            $message_kr = '개발 요청을 검색하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(DevelopmentUpdateRequest $request, DevelopmentRequest $developmentRequest)
    {
        try {
            $data = $request->validated();
            $updatedDevelopmentRequest = $this->service->update($developmentRequest, $data);
            $message = 'Development request updated successfully.';
            $message_kr = '개발 요청이 성공적으로 업데이트되었습니다.';
            return $this->apiResponse($updatedDevelopmentRequest, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = 'Error updating development request.';
            $message_kr = '개발 요청을 업데이트하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $this->service->delete($id);
            $message = 'Development request deleted successfully.';
            $message_kr = '개발 요청이 성공적으로 삭제되었습니다.';
            return $this->apiResponse(null, $message, $message_kr, true, 200);
        } catch (\Exception $e) {
            $message = $e->getMessage();
            $message_kr = '개발 요청을 삭제하는 중 오류가 발생했습니다.';
            return $this->apiResponse(null, $message, $message_kr, false, 500);
        }
    }
}
