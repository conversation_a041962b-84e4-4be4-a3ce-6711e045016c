<?php

namespace App\Http\Requests\DevelopmentItem;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class DevelopmentItemStoreRequest extends FormRequest
{
    use HelperTrait;
    
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can create development items
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'unit' => 'nullable|string|max:50',
            'category' => 'nullable|string|max:100',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름이 필요합니다.',
            ],
            'name.max' => [
                'en' => 'Name cannot exceed 255 characters.',
                'kr' => '이름은 255자를 초과할 수 없습니다.',
            ],
            'price.required' => [
                'en' => 'Price is required.',
                'kr' => '가격이 필요합니다.',
            ],
            'price.numeric' => [
                'en' => 'Price must be a number.',
                'kr' => '가격은 숫자여야 합니다.',
            ],
            'price.min' => [
                'en' => 'Price cannot be negative.',
                'kr' => '가격은 음수일 수 없습니다.',
            ],
        ];
    }
}
