<?php

namespace App\Http\Requests\Profile;

use Illuminate\Foundation\Http\FormRequest;

class PasswordUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'current_password' => 'required|string|current_password',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{6,}$/',
                'confirmed',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'current_password.required' => [
                'en' => 'Current password is required.',
                'kr' => '현재 비밀번호가 필요합니다.',
            ],
            'current_password.current_password' => [
                'en' => 'Current password is incorrect.',
                'kr' => '현재 비밀번호가 올바르지 않습니다.',
            ],
            'password.required' => [
                'en' => 'New password is required.',
                'kr' => '새 비밀번호가 필요합니다.',
            ],
            'password.min' => [
                'en' => 'New password must be at least 8 characters.',
                'kr' => '새 비밀번호는 최소 8자 이상이어야 합니다.',
            ],
            'password.regex' => [
                'en' => 'New password must include at least one uppercase letter, one lowercase letter, one number, and one special character.',
                'kr' => '새 비밀번호는 대문자, 소문자, 숫자, 특수 문자를 각각 하나 이상 포함해야 합니다.',
            ],
            'password.confirmed' => [
                'en' => 'Password confirmation does not match.',
                'kr' => '비밀번호 확인이 일치하지 않습니다.',
            ],
        ];
    }
}
