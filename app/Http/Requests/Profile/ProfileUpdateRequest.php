<?php

namespace App\Http\Requests\Profile;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = Auth::id();
        
        return [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $userId,
            'username' => 'sometimes|required|string|unique:users,username,' . $userId,
            'phone_no' => 'nullable|string|unique:users,phone_no,' . $userId,
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름이 필요합니다.',
            ],
            'email.required' => [
                'en' => 'Email is required.',
                'kr' => '이메일이 필요합니다.',
            ],
            'email.email' => [
                'en' => 'Email must be a valid email address.',
                'kr' => '이메일은 유효한 이메일 주소여야 합니다.',
            ],
            'email.unique' => [
                'en' => 'Email is already taken.',
                'kr' => '이미 사용 중인 이메일입니다.',
            ],
            'username.required' => [
                'en' => 'Username is required.',
                'kr' => '사용자 이름이 필요합니다.',
            ],
            'username.unique' => [
                'en' => 'Username is already taken.',
                'kr' => '이미 사용 중인 사용자 이름입니다.',
            ],
            'phone_no.unique' => [
                'en' => 'Phone number is already taken.',
                'kr' => '이미 사용 중인 전화번호입니다.',
            ],
        ];
    }
}
