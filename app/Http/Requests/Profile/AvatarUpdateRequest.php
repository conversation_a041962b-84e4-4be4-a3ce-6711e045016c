<?php

namespace App\Http\Requests\Profile;

use Illuminate\Foundation\Http\FormRequest;

class AvatarUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'avatar.required' => [
                'en' => 'Profile picture is required.',
                'kr' => '프로필 사진이 필요합니다.',
            ],
            'avatar.image' => [
                'en' => 'The file must be an image.',
                'kr' => '파일은 이미지여야 합니다.',
            ],
            'avatar.mimes' => [
                'en' => 'The image must be a file of type: jpeg, png, jpg, gif.',
                'kr' => '이미지는 jpeg, png, jpg, gif 유형의 파일이어야 합니다.',
            ],
            'avatar.max' => [
                'en' => 'The image may not be greater than 2MB.',
                'kr' => '이미지는 2MB를 초과할 수 없습니다.',
            ],
        ];
    }
}
