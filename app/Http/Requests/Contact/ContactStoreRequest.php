<?php

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;

use App\Models\User;

class ContactStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|exists:users,username',
            'contract_name' => 'required|string|max:255',
            'contract_details' => 'nullable|string',
            'page_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|in:New Lead,Development,Maintenance,Ended',
            'amount' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '연락처 이름은 필수 항목입니다.',
            ],
            'contract_name.string' => [
                'en' => 'Contract name should be a string.',
                'kr' => '연락처 이름은 문자열이어야 합니다.',
            ],
            'contract_name.max' => [
                'en' => 'Contract name should not exceed 255 characters.',
                'kr' => '연락처 이름은 255자를 초과할 수 없습니다.',
            ],
            'contract_details.string' => [
                'en' => 'Contract details should be a string.',
                'kr' => '연락처 세부 정보는 문자열이어야 합니다.',
            ],
            'page_url.url' => [
                'en' => 'Page URL should be a valid URL.',
                'kr' => '페이지 URL은 유효한 URL이어야 합니다.',
            ],
            'start_date.date' => [
                'en' => 'Start date should be a valid date.',
                'kr' => '시작 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.date' => [
                'en' => 'End date should be a valid date.',
                'kr' => '종료 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.after_or_equal' => [
                'en' => 'End date should be after or equal to the start date.',
                'kr' => '종료 날짜는 시작 날짜 이후 또는 같아야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: New Lead, Development, Maintenance, Ended.',
                'kr' => '상태는 다음 값 중 하나여야 합니다: New Lead, Development, Maintenance, Ended.',
            ],
        ];
    }

    /**
     * Extract only the required validated fields.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validatedData = $this->only([
            'contract_name',
            'contract_details',
            'page_url',
            'start_date',
            'end_date',
            'status',
            'amount',
        ]);



        $validatedData['status'] = $validatedData['status'] ?? 'New Lead';

        return $validatedData;
    }
}
