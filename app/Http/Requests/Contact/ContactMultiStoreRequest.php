<?php

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;

class ContactMultiStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.name' => 'required|string|max:255',
            '*.email' => 'required|email|unique:contacts,email',
            '*.phone' => 'required|string|max:20',
        ];
    }

    /**
     * Get the custom validation messages for rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            '*.name.required' => [
                'en' => 'Name is required for all contacts.',
                'kr' => '모든 연락처에 이름이 필요합니다.',
            ],
            '*.name.string' => [
                'en' => 'Name should be a valid string for all contacts.',
                'kr' => '모든 연락처의 이름은 유효한 문자열이어야 합니다.',
            ],
            '*.name.max' => [
                'en' => 'Name should not exceed 255 characters for any contact.',
                'kr' => '모든 연락처의 이름은 255자를 초과할 수 없습니다.',
            ],
            '*.email.required' => [
                'en' => 'Email is required for all contacts.',
                'kr' => '모든 연락처에 이메일이 필요합니다.',
            ],
            '*.email.email' => [
                'en' => 'Email should be a valid email address for all contacts.',
                'kr' => '모든 연락처의 이메일은 유효한 이메일 주소여야 합니다.',
            ],
            '*.email.unique' => [
                'en' => 'Email must be unique for all contacts.',
                'kr' => '모든 연락처의 이메일은 고유해야 합니다.',
            ],
            '*.phone.required' => [
                'en' => 'Phone number is required for all contacts.',
                'kr' => '모든 연락처에 전화번호가 필요합니다.',
            ],
            '*.phone.string' => [
                'en' => 'Phone number should be a valid string for all contacts.',
                'kr' => '모든 연락처의 전화번호는 유효한 문자열이어야 합니다.',
            ],
            '*.phone.max' => [
                'en' => 'Phone number should not exceed 20 characters for any contact.',
                'kr' => '모든 연락처의 전화번호는 20자를 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Process and generate the validated data.
     *
     * @return array<mixed>
     */
    public function validatedData(): array
    {
        return array_map(function ($contact) {
            return [
                'name' => ucfirst($contact['name']),
                'email' => strtolower($contact['email']),
                'phone' => preg_replace('/[^0-9]/', '', $contact['phone']), // Remove non-numeric characters
            ];
        }, $this->validated());
    }
}
