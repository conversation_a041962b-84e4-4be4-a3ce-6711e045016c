<?php

namespace App\Http\Requests\Contact;

use Illuminate\Foundation\Http\FormRequest;
use App\Trait\HelperTrait;

class ContactUpdateRequest extends FormRequest
{
    use HelperTrait;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'contract_name' => 'sometimes|required|string|max:255',
            'contract_details' => 'nullable|string',
            'page_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'sometimes|required|in:New Lead,Development,Maintenance,Ended',
            'amount' => 'nullable|numeric|min:0',
            'rfp_images' => 'nullable|array',
            'rfp_images.*' => 'image|mimes:jpeg,png,jpg,gif,svg',
            'rfp_files' => 'nullable|array',
            'rfp_files.*' => 'file|mimes:pdf,doc,docx|max:2048',
            'rfp_status' => 'sometimes|required|in:Applied,Accepted,Completed',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '연락처 이름은 필수 항목입니다.',
            ],
            'contract_name.string' => [
                'en' => 'Contract name should be a string.',
                'kr' => '연락처 이름은 문자열이어야 합니다.',
            ],
            'contract_name.max' => [
                'en' => 'Contract name should not exceed 255 characters.',
                'kr' => '연락처 이름은 255자를 초과할 수 없습니다.',
            ],
            'contract_details.string' => [
                'en' => 'Contract details should be a string.',
                'kr' => '연락처 세부 정보는 문자열이어야 합니다.',
            ],
            'page_url.url' => [
                'en' => 'Page URL should be a valid URL.',
                'kr' => '페이지 URL은 유효한 URL이어야 합니다.',
            ],
            'start_date.date' => [
                'en' => 'Start date should be a valid date.',
                'kr' => '시작 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.date' => [
                'en' => 'End date should be a valid date.',
                'kr' => '종료 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.after_or_equal' => [
                'en' => 'End date should be after or equal to the start date.',
                'kr' => '종료 날짜는 시작 날짜 이후 또는 같아야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: New Lead, Development, Maintenance, Ended.',
                'kr' => '상태는 다음 값 중 하나여야 합니다: New Lead, Development, Maintenance, Ended.',
            ],
            'rfp_images.*.image' => [
                'en' => 'RFP images should be valid image files.',
                'kr' => 'RFP 이미지는 유효한 이미지 파일이어야 합니다.',
            ],
            'rfp_images.*.mimes' => [
                'en' => 'RFP images should be either jpeg, png, jpg, gif, or svg.',
                'kr' => 'RFP 이미지는 jpeg, png, jpg, gif, svg 중 하나여야 합니다.',
            ],
            'rfp_files.*.file' => [
                'en' => 'RFP files should be valid files.',
                'kr' => 'RFP 파일은 유효한 파일이어야 합니다.',
            ],
            'rfp_files.*.mimes' => [
                'en' => 'RFP files should be either pdf, doc, or docx.',
                'kr' => 'RFP 파일은 pdf, doc, docx 중 하나여야 합니다.',
            ],
            'rfp_files.*.max' => [
                'en' => 'RFP files should not exceed 2048 kilobytes.',
                'kr' => 'RFP 파일은 2048킬로바이트를 초과할 수 없습니다.',
            ],
            'rfp_status.required' => [
                'en' => 'RFP status is required.',
                'kr' => 'RFP 상태는 필수 항목입니다.',
            ],
            'rfp_status.in' => [
                'en' => 'RFP status must be one of the following: Applied, Accepted, Completed',
                'kr' => 'RFP 상태는 다음 값 중 하나여야 합니다: Applied, Accepted, Completed',
            ],
        ];
    }

    /**
     * Extract only the required validated fields.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validatedData = $this->only([
            'contract_name',
            'contract_details',
            'page_url',
            'start_date',
            'end_date',
            'amount',
            'status',
            'rfp_status'
        ]);

        $files = [];
        $images = [];

        if ($this->hasFile('rfp_files')) {
            $count = 0;
            foreach ($this->file('rfp_files') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_files'); 
                $file->move($destinationPath, $filename); 

                $files[] = 'rfp_files/' . $filename; 
            }
        }
    
        if ($this->hasFile('rfp_images')) {
            $count = 0;
            foreach ($this->file('rfp_images') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_images'); 
                $file->move($destinationPath, $filename); 

                $images[] = 'rfp_images/' . $filename; 
            }
        }
        if (isset($validatedData['rfp_status'])) {

            if ($validatedData['rfp_status'] == 'Applied') {
                $validatedData['application_date'] = now();
            }

            if ($validatedData['rfp_status'] == 'Accepted') {
                $validatedData['acceptance_date'] = now();
            }

            if ($validatedData['rfp_status'] == 'Completed') {
                $validatedData['completion_date'] = now();
            }
        }

        $data = [
            'contract' => $validatedData,
            'rfp_files' => $files,
            'rfp_images' => $images
        ];

        return $data;
        
    }
}
