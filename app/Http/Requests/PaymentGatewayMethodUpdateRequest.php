<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PaymentGatewayMethodUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'method_name' => ['required', 'string', 'max:255'],
            'account_number' => ['nullable', 'string', 'max:255'],
            'is_active' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'method_name.required' => [
                'en' => 'The method name is required.',
                'kr' => '방법 이름은 필수 항목입니다.',
            ],
            'method_name.max' => [
                'en' => 'The method name may not be greater than 255 characters.',
                'kr' => '방법 이름은 255자를 초과할 수 없습니다.',
            ],
            'account_number.max' => [
                'en' => 'The account number may not be greater than 255 characters.',
                'kr' => '계좌 번호는 255자를 초과할 수 없습니다.',
            ],
            'is_active.boolean' => [
                'en' => 'The is active field must be true or false.',
                'kr' => '활성 필드는 true 또는 false여야 합니다.',
            ],
        ];
    }

    /**
     * Get the validated data from the request.
     *
     * @return array
     */
    public function validatedData(): array
    {
        return $this->validated();
    }
}
