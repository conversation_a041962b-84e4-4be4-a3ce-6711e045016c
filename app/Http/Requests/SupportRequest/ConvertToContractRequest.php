<?php

namespace App\Http\Requests\SupportRequest;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class ConvertToContractRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'support_request_id' => 'required|exists:penguin_support_requests,id',
            'customer_id' => 'required|exists:users,id',
            'start_date' => 'nullable|date',
            'amount' => 'required|numeric|min:0',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'support_request_id.required' => [
                'en' => 'Support request ID is required.',
                'kr' => '지원 요청 ID는 필수 항목입니다.',
            ],
            'support_request_id.exists' => [
                'en' => 'The selected support request does not exist.',
                'kr' => '선택한 지원 요청이 존재하지 않습니다.',
            ],
            'amount.required' => [
                'en' => 'Amount is required.',
                'kr' => '금액은 필수 항목입니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        return $this->only([
            'customer_id',
            'support_request_id',
            'start_date',
            'amount',
        ]);
    }
}
