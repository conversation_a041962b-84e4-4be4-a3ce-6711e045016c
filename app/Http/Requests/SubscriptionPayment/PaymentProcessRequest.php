<?php

namespace App\Http\Requests\SubscriptionPayment;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class PaymentProcessRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Admin users can process payments, visitors can process their own payments
        return auth()->check() && (
            auth()->user()->hasRole('admin') ||
            auth()->user()->hasRole('visitor')
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'nullable|numeric|min:0|max:*********.99',
            'transaction_id' => 'nullable|string|max:255',
            'payment_method' => 'nullable|in:credit_card,bank_transfer,paypal,stripe,manual,other',
            'payment_gateway' => 'nullable|string|max:100',
            'payment_details' => 'nullable|array',
            'payment_notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'amount.numeric' => [
                'en' => 'Amount must be a valid number.',
                'kr' => '금액은 유효한 숫자여야 합니다.',
            ],
            'amount.min' => [
                'en' => 'Amount must be greater than or equal to 0.',
                'kr' => '금액은 0 이상이어야 합니다.',
            ],
            'amount.max' => [
                'en' => 'Amount cannot exceed 999,999,999.99.',
                'kr' => '금액은 999,999,999.99를 초과할 수 없습니다.',
            ],
            'payment_method.in' => [
                'en' => 'Payment method must be one of: credit_card, bank_transfer, paypal, stripe, manual, other.',
                'kr' => '결제 방법은 다음 중 하나여야 합니다: credit_card, bank_transfer, paypal, stripe, manual, other.',
            ],
            'transaction_id.max' => [
                'en' => 'Transaction ID cannot exceed 255 characters.',
                'kr' => '거래 ID는 255자를 초과할 수 없습니다.',
            ],
            'payment_gateway.max' => [
                'en' => 'Payment gateway cannot exceed 100 characters.',
                'kr' => '결제 게이트웨이는 100자를 초과할 수 없습니다.',
            ],
            'payment_notes.max' => [
                'en' => 'Payment notes cannot exceed 500 characters.',
                'kr' => '결제 메모는 500자를 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'amount' => 'amount',
            'transaction_id' => 'transaction ID',
            'payment_method' => 'payment method',
            'payment_gateway' => 'payment gateway',
            'payment_details' => 'payment details',
            'payment_notes' => 'payment notes',
        ];
    }
}
