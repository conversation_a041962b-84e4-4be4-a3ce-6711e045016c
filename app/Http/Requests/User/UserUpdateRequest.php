<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $userId = $this->route('user');
        
        return [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $userId,
            'username' => 'sometimes|required|string|unique:users,username,' . $userId,
            'phone_no' => 'nullable|string|unique:users,phone_no,' . $userId,
            'password' => [
                'sometimes',
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{6,}$/',
                'confirmed',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름은 필수입니다.',
            ],
            'email.required' => [
                'en' => 'Email is required.',
                'kr' => '이메일은 필수입니다.',
            ],
            'email.unique' => [
                'en' => 'Email has already been taken.',
                'kr' => '이미 사용 중인 이메일입니다.',
            ],
            'username.unique' => [
                'en' => 'Username has already been taken.',
                'kr' => '이미 사용 중인 사용자 이름입니다.',
            ],
        ];
    }
}