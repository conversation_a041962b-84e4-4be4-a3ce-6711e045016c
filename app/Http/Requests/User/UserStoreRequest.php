<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'username' => 'required|string|unique:users|max:255',
            'phone_no' => 'nullable|string|unique:users',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{6,}$/',
                'confirmed',
            ],
            'role' => 'required|string|in:admin,bacbon,visitor',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름은 필수입니다.',
            ],
            'email.required' => [
                'en' => 'Email is required.',
                'kr' => '이메일은 필수입니다.',
            ],
            'email.unique' => [
                'en' => 'Email has already been taken.',
                'kr' => '이미 사용 중인 이메일입니다.',
            ],
            'username.required' => [
                'en' => 'Username is required.',
                'kr' => '사용자 이름은 필수입니다.',
            ],
            'username.unique' => [
                'en' => 'Username has already been taken.',
                'kr' => '이미 사용 중인 사용자 이름입니다.',
            ],
            'password.required' => [
                'en' => 'Password is required.',
                'kr' => '비밀번호는 필수입니다.',
            ],
            'password.regex' => [
                'en' => 'Password must include uppercase, lowercase, number, and special character.',
                'kr' => '비밀번호는 대문자, 소문자, 숫자 및 특수 문자를 포함해야 합니다.',
            ],
            'role.required' => [
                'en' => 'Role is required.',
                'kr' => '역할은 필수입니다.',
            ],
            'role.in' => [
                'en' => 'Role must be one of: admin, bacbon, visitor.',
                'kr' => '역할은 다음 중 하나여야 합니다: 관리자, 백본, 방문자.',
            ],
        ];
    }
}