<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Trait\HelperTrait;

class DevelopmentUpdateRequest extends FormRequest
{
    use HelperTrait;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'contact_id' => ['required', 'exists:penguin_contracts,id'],
            'request_title' => ['required', 'string', 'max:255'],
            'request_details' => ['nullable', 'string'],
            'status' => ['required', 'in:Pending,Rejected,Complete'],
            'remarks' => ['nullable', 'string'],
            'file' => ['nullable', 'mimes:pdf,doc,docx,txt,jpeg,png,jpg,gif', 'max:2048'],
        ];
    }

    public function messages(): array
    {
        return [
            'contact_id.required' => [
                'en' => 'Contact ID is required.',
                'kr' => '연락처 ID는 필수 항목입니다.',
            ],
            'contact_id.exists' => [
                'en' => 'The selected contact ID does not exist.',
                'kr' => '선택한 연락처 ID가 존재하지 않습니다.',
            ],
            'request_title.required' => [
                'en' => 'The request title is required.',
                'kr' => '요청 제목은 필수 항목입니다.',
            ],
            'request_title.string' => [
                'en' => 'The request title must be a string.',
                'kr' => '요청 제목은 문자열이어야 합니다.',
            ],
            'request_title.max' => [
                'en' => 'The request title must not exceed 255 characters.',
                'kr' => '요청 제목은 255자를 초과할 수 없습니다.',
            ],
            'request_details.string' => [
                'en' => 'The request details must be a string.',
                'kr' => '요청 세부 정보는 문자열이어야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: Pending, Rejected, Complete.',
                'kr' => '상태는 다음 중 하나여야 합니다: Pending, Rejected, Complete.',
            ],
            'remarks.string' => [
                'en' => 'Remarks must be a string.',
                'kr' => '비고는 문자열이어야 합니다.',
            ],
            'file.mimes' => [
                'en' => 'The file must be a type of: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
                'kr' => '파일은 다음 유형 중 하나여야 합니다: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
            ],
            'file.max' => [
                'en' => 'The file must not be larger than :max kilobytes.',
                'kr' => '파일은 :max 킬로바이트를 초과할 수 없습니다.',
            ],
        ];
    }

    public function validatedData()
    {
        $validatedData = $this->only([
            'contact_id',
            'request_title',
            'request_details',
            'status',
            'remarks',
        ]);

        if ($this->hasFile('file')) {
            $validatedData['file'] = $this->imageUpload($this, 'file', 'development_requests');
        }
        
        return $validatedData;
    }

}