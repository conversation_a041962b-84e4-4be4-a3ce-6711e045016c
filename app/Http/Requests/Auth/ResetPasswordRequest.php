<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{6,}$/',
                'confirmed',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'id.required' => [
                'en' => 'OTP ID is required.',
                'kr' => 'OTP ID가 필요합니다.',
            ],
            'password.required' => [
                'en' => 'Password is required.',
                'kr' => '비밀번호가 필요합니다.',
            ],
            'password.min' => [
                'en' => 'Password must be at least 8 characters.',
                'kr' => '비밀번호는 최소 8자여야 합니다.',
            ],
            'password.confirmed' => [
                'en' => 'Password confirmation does not match.',
                'kr' => '비밀번호 확인이 일치하지 않습니다.',
            ],
            'password.regex' => [
                'en' => 'Password must include uppercase, lowercase, number, and special character.',
                'kr' => '비밀번호는 대소문자, 숫자, 특수 문자를 포함해야 합니다.',
            ],
        ];
    }
}
