<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'login' => 'required|string',
            'password' => 'required|string',
            'remember_me' => 'nullable|boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'login.required' => [
                'en' => 'User ID or Email is required.',
                'kr' => '사용자 ID 또는 이메일이 필요합니다.',
            ],
            'login.string' => [
                'en' => 'User ID or Email must be a string.',
                'kr' => '사용자 ID 또는 이메일은 문자열이어야 합니다.',
            ],
            'password.required' => [
                'en' => 'Password is required.',
                'kr' => '비밀번호가 필요합니다.',
            ],
            'password.string' => [
                'en' => 'Password must be a string.',
                'kr' => '비밀번호는 문자열이어야 합니다.',
            ],
        ];
    }
}
