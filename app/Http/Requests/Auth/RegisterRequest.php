<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable|string',
            'email' => 'required|email|unique:users',
            'username' => 'required|unique:users',
            'phone_no' => 'nullable|unique:users',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{6,}$/',
                'confirmed',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름은 필수입니다.',
            ],
            'email.required' => [
                'en' => 'Email is required.',
                'kr' => '이메일은 필수입니다.',
            ],
            'email.unique' => [
                'en' => 'Email has already been taken.',
                'kr' => '이 이메일은 이미 사용 중입니다.',
            ],
            'username.required' => [
                'en' => 'Username is required.',
                'kr' => '사용자 이름은 필수입니다.',
            ],
            'username.unique' => [
                'en' => 'Username has already been taken.',
                'kr' => '이 사용자 이름은 이미 사용 중입니다.',
            ],
            'phone_no.unique' => [
                'en' => 'Phone number has already been taken.',
                'kr' => '이 전화번호는 이미 사용 중입니다.',
            ],
            'password.required' => [
                'en' => 'Password is required.',
                'kr' => '비밀번호는 필수입니다.',
            ],
            'password.min' => [
                'en' => 'Password must be at least 8 characters.',
                'kr' => '비밀번호는 최소 8자 이상이어야 합니다.',
            ],
            'password.regex' => [
                'en' => 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character.',
                'kr' => '비밀번호는 대문자, 소문자, 숫자, 특수 문자를 각각 최소 하나 이상 포함해야 합니다.',
            ],
            'password.confirmed' => [
                'en' => 'Password confirmation does not match.',
                'kr' => '비밀번호 확인이 일치하지 않습니다.',
            ],
        ];
    }

}