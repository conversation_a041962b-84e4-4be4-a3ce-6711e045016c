<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class VerifyOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'otp' => 'required|string',
            'email' => 'required|string|email',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'otp.required' => [
                'en' => 'OTP is required.',
                'kr' => 'OTP가 필요합니다.',
            ],
            'otp.string' => [
                'en' => 'OTP must be a string.',
                'kr' => 'OTP는 문자열이어야 합니다.',
            ],
            'email.required' => [
                'en' => 'Email is required.',
                'kr' => '이메일이 필요합니다.',
            ],
            'email.string' => [
                'en' => 'Email must be a string.',
                'kr' => '이메일은 문자열이어야 합니다.',
            ],
            'email.email' => [
                'en' => 'Email must be a valid email address.',
                'kr' => '이메일은 유효한 이메일 주소여야 합니다.',
            ],
        ];
    }
}
