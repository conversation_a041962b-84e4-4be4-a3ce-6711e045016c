<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class VerifySecretRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'secret' => 'required|string',
            'username' => 'required|string',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'secret.required' => [
                'en' => 'Secret is required.',
                'kr' => '비밀 코드가 필요합니다.',
            ],
            'secret.string' => [
                'en' => 'Secret must be a string.',
                'kr' => '비밀 코드는 문자열이어야 합니다.',
            ],
            'username.required' => [
                'en' => 'Username is required.',
                'kr' => '사용자 이름이 필요합니다.',
            ],
            'username.string' => [
                'en' => 'Username must be a string.',
                'kr' => '사용자 이름은 문자열이어야 합니다.',
            ],
        ];
    }
}
