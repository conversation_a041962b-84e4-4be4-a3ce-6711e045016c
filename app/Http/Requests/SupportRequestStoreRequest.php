<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Trait\HelperTrait;

class SupportRequestStoreRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow both authenticated and guest users
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'request_type' => ['required', 'string', 'max:255'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'base_construction_cost' => ['nullable', 'numeric', 'min:0'],
            'monthly_subscription_fee' => ['nullable', 'numeric', 'min:0'],
            'additional_items_total' => ['nullable', 'numeric', 'min:0'],
            'total_construction_cost' => ['nullable', 'numeric', 'min:0'],
            'files' => ['nullable', 'array'],
            'files.*' => ['file', 'mimes:pdf,ppt,doc,docx,txt,jpeg,png,jpg,gif', 'max:2048'],
        ];

        // If user is not authenticated, require name and email
        if (!auth('sanctum')->check()) {
            $rules['name'] = ['required', 'string', 'max:255'];
            $rules['email'] = ['required', 'email', 'max:255'];
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'request_type.required' => [
                'en' => 'Request type is required.',
                'kr' => '요청 유형은 필수 항목입니다.',
            ],
            'title.required' => [
                'en' => 'Title is required.',
                'kr' => '제목은 필수 항목입니다.',
            ],
            'description.required' => [
                'en' => 'Description is required.',
                'kr' => '설명은 필수 항목입니다.',
            ],
            'name.required' => [
                'en' => 'Name is required for guest users.',
                'kr' => '게스트 사용자의 경우 이름이 필요합니다.',
            ],
            'email.required' => [
                'en' => 'Email is required for guest users.',
                'kr' => '게스트 사용자의 경우 이메일이 필요합니다.',
            ],
            'email.email' => [
                'en' => 'Please provide a valid email address.',
                'kr' => '유효한 이메일 주소를 입력하세요.',
            ],
            'files.*.mimes' => [
                'en' => 'Files must be of type: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
                'kr' => '파일은 다음 유형이어야 합니다: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
            ],
            'files.*.max' => [
                'en' => 'Files may not be larger than 2MB.',
                'kr' => '파일은 2MB보다 클 수 없습니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $data = $this->only([
            'request_type',
            'title',
            'description',
            'base_construction_cost',
            'monthly_subscription_fee',
            'additional_items_total',
            'total_construction_cost',
        ]);

        // If user is authenticated, add user_id
        if (auth('sanctum')->check()) {
            $data['user_id'] = auth('sanctum')->id();
        } else {
            // For guest users, add name and email
            $data['name'] = $this->name;
            $data['email'] = $this->email;
        }

        // Process file uploads
        $files = [];
        if ($this->hasFile('files')) {
            foreach ($this->file('files') as $file) {
                try {
                    // Get file information before moving it
                    $originalName = $file->getClientOriginalName();
                    $mimeType = $file->getClientMimeType();
                    $size = $file->getSize();

                    // Generate filename and move file
                    $filename = time() . '_' . $originalName;
                    $destinationPath = public_path('uploads/support_requests');

                    // Create directory if it doesn't exist
                    if (!file_exists($destinationPath)) {
                        mkdir($destinationPath, 0755, true);
                    }

                    $file->move($destinationPath, $filename);
                    $filePath = 'support_requests/' . $filename;

                    $files[] = [
                        'path' => $filePath,
                        'original_name' => $originalName,
                        'type' => $mimeType,
                        'size' => $size,
                    ];
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Log::error('Error uploading file: ' . $e->getMessage());
                }
            }
        }

        $data['files'] = $files;

        // Process development items
        $developmentItems = [];
        if ($this->has('development_items')) {
            // Handle both JSON string and array formats
            $items = $this->development_items;
            if (is_string($items)) {
                $items = json_decode($items, true);
            }

            if (is_array($items)) {
                foreach ($items as $item) {
                    $developmentItems[] = [
                        'development_item_id' => $item['id'] ?? null,
                        'name' => $item['name'],
                        'price' => $item['price'],
                        'quantity' => $item['quantity'],
                        'total' => $item['total'],
                    ];
                }
            }
        }
        $data['development_items'] = $developmentItems;

        return $data;
    }
}
