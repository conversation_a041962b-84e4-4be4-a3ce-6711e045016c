<?php

namespace App\Http\Requests\Subscription;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class SubscriptionUpdateRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can update subscriptions
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'monthly_amount' => 'nullable|numeric|min:0|max:999999999.99',
            'end_date' => 'nullable|date',
            'status' => 'nullable|in:active,paused,cancelled,expired',
            'billing_cycle' => 'nullable|in:monthly,quarterly,yearly',
            'billing_day' => 'nullable|integer|min:1|max:28',
            'auto_renewal' => 'nullable|boolean',
            'grace_period_days' => 'nullable|integer|min:0|max:30',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'monthly_amount.numeric' => [
                'en' => 'Monthly amount must be a valid number.',
                'kr' => '월 금액은 유효한 숫자여야 합니다.',
            ],
            'monthly_amount.min' => [
                'en' => 'Monthly amount cannot be negative.',
                'kr' => '월 금액은 음수일 수 없습니다.',
            ],
            'status.in' => [
                'en' => 'Status must be active, paused, cancelled, or expired.',
                'kr' => '상태는 활성, 일시정지, 취소 또는 만료여야 합니다.',
            ],
            'billing_cycle.in' => [
                'en' => 'Billing cycle must be monthly, quarterly, or yearly.',
                'kr' => '청구 주기는 월별, 분기별 또는 연간이어야 합니다.',
            ],
            'billing_day.min' => [
                'en' => 'Billing day must be between 1 and 28.',
                'kr' => '청구일은 1일에서 28일 사이여야 합니다.',
            ],
            'billing_day.max' => [
                'en' => 'Billing day must be between 1 and 28.',
                'kr' => '청구일은 1일에서 28일 사이여야 합니다.',
            ],
            'grace_period_days.max' => [
                'en' => 'Grace period cannot exceed 30 days.',
                'kr' => '유예 기간은 30일을 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string boolean to actual boolean
        if ($this->has('auto_renewal') && is_string($this->auto_renewal)) {
            $this->merge([
                'auto_renewal' => filter_var($this->auto_renewal, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
