<?php

namespace App\Http\Requests\Subscription;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class SubscriptionStoreRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can create subscriptions manually
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'contract_id' => 'required|exists:penguin_contracts,id',
            'customer_id' => 'required|exists:users,id',
            'monthly_amount' => 'required|numeric|min:0|max:999999999.99',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly',
            'billing_day' => 'required|integer|min:1|max:28',
            'auto_renewal' => 'nullable|boolean',
            'grace_period_days' => 'nullable|integer|min:0|max:30',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'contract_id.required' => [
                'en' => 'Contract is required.',
                'kr' => '계약이 필요합니다.',
            ],
            'contract_id.exists' => [
                'en' => 'Selected contract does not exist.',
                'kr' => '선택한 계약이 존재하지 않습니다.',
            ],
            'customer_id.required' => [
                'en' => 'Customer is required.',
                'kr' => '고객이 필요합니다.',
            ],
            'customer_id.exists' => [
                'en' => 'Selected customer does not exist.',
                'kr' => '선택한 고객이 존재하지 않습니다.',
            ],
            'monthly_amount.required' => [
                'en' => 'Monthly amount is required.',
                'kr' => '월 금액이 필요합니다.',
            ],
            'monthly_amount.numeric' => [
                'en' => 'Monthly amount must be a valid number.',
                'kr' => '월 금액은 유효한 숫자여야 합니다.',
            ],
            'monthly_amount.min' => [
                'en' => 'Monthly amount cannot be negative.',
                'kr' => '월 금액은 음수일 수 없습니다.',
            ],
            'start_date.required' => [
                'en' => 'Start date is required.',
                'kr' => '시작 날짜가 필요합니다.',
            ],
            'start_date.after_or_equal' => [
                'en' => 'Start date must be today or later.',
                'kr' => '시작 날짜는 오늘 이후여야 합니다.',
            ],
            'end_date.after' => [
                'en' => 'End date must be after start date.',
                'kr' => '종료 날짜는 시작 날짜 이후여야 합니다.',
            ],
            'billing_cycle.required' => [
                'en' => 'Billing cycle is required.',
                'kr' => '청구 주기가 필요합니다.',
            ],
            'billing_cycle.in' => [
                'en' => 'Billing cycle must be monthly, quarterly, or yearly.',
                'kr' => '청구 주기는 월별, 분기별 또는 연간이어야 합니다.',
            ],
            'billing_day.required' => [
                'en' => 'Billing day is required.',
                'kr' => '청구일이 필요합니다.',
            ],
            'billing_day.min' => [
                'en' => 'Billing day must be between 1 and 28.',
                'kr' => '청구일은 1일에서 28일 사이여야 합니다.',
            ],
            'billing_day.max' => [
                'en' => 'Billing day must be between 1 and 28.',
                'kr' => '청구일은 1일에서 28일 사이여야 합니다.',
            ],
            'grace_period_days.max' => [
                'en' => 'Grace period cannot exceed 30 days.',
                'kr' => '유예 기간은 30일을 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'contract_id' => 'contract',
            'customer_id' => 'customer',
            'monthly_amount' => 'monthly amount',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'billing_cycle' => 'billing cycle',
            'billing_day' => 'billing day',
            'auto_renewal' => 'auto renewal',
            'grace_period_days' => 'grace period days',
            'notes' => 'notes',
            'metadata' => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('auto_renewal')) {
            $this->merge(['auto_renewal' => true]);
        }

        if (!$this->has('grace_period_days')) {
            $this->merge(['grace_period_days' => 7]);
        }

        if (!$this->has('billing_day')) {
            $this->merge(['billing_day' => 1]);
        }

        // Convert string boolean to actual boolean
        if ($this->has('auto_renewal') && is_string($this->auto_renewal)) {
            $this->merge([
                'auto_renewal' => filter_var($this->auto_renewal, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
