<?php

namespace App\Http\Requests\ContractDraft;

use App\Models\ContractDraft;
use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class ContractDraftResponseRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Get the draft
        $draft = ContractDraft::find($this->route('id'));

        if (!$draft) {
            return false;
        }

        // Only the customer of the contract can respond to drafts
        return auth()->check() &&
               auth()->user()->hasRole('visitor') &&
               auth()->id() === $draft->contract->customer_id;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'required|in:Accepted,Declined',
            'feedback' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be either Accepted or Declined.',
                'kr' => '상태는 수락 또는 거부 중 하나여야 합니다.',
            ],
            'feedback.max' => [
                'en' => 'Feedback may not be longer than 1000 characters.',
                'kr' => '피드백은 1000자를 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        return [
            'status' => $this->status,
            'feedback' => $this->feedback,
        ];
    }
}
