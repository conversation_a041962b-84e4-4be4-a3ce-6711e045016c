<?php

namespace App\Http\Requests\ContractDraft;

use App\Trait\HelperTrait;
use App\Services\FileUploadHelper;
use Illuminate\Foundation\Http\FormRequest;

class ContractDraftStoreRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only bacbon users can create contract drafts
        return auth()->check() && auth()->user()->hasRole('bacbon');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'contract_id' => 'required|exists:penguin_contracts,id',
            'description' => 'nullable|string',
            'files' => 'nullable|array',
            'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB max
            'urls' => 'nullable|array'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'contract_id.required' => [
                'en' => 'Contract ID is required.',
                'kr' => '계약 ID는 필수 항목입니다.',
            ],
            'contract_id.exists' => [
                'en' => 'The selected contract does not exist.',
                'kr' => '선택한 계약이 존재하지 않습니다.',
            ],
            'files.*.mimes' => [
                'en' => 'Files must be of type: pdf, doc, docx, jpg, jpeg, png.',
                'kr' => '파일은 다음 유형이어야 합니다: pdf, doc, docx, jpg, jpeg, png.',
            ],
            'files.*.max' => [
                'en' => 'Files may not be larger than 5MB.',
                'kr' => '파일은 5MB보다 클 수 없습니다.',
            ],
            'urls.*.url' => [
                'en' => 'Each URL must be a valid URL.',
                'kr' => '각 URL은 유효한 URL이어야 합니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $data = [
            'contract_id' => $this->contract_id,
            'description' => $this->description,
            'files' => [],
            'urls' => $this->urls ?? [],
        ];

        // Process file uploads using S3
        $fileUploadHelper = new FileUploadHelper();
        $files = $fileUploadHelper->processFileUploads($this, 'files', 'contract_drafts');
        $data['files'] = $files;

        return $data;
    }
}
