<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Trait\HelperTrait;

class PaymentGatewayStoreRequest extends FormRequest
{
    use HelperTrait;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:1024'], // Updated logo validation
            'remarks' => ['nullable', 'string'],
            'status' => ['nullable', 'string', 'in:Active,Inactive'],
        ];
    }

    
    /**
     * Get the validation messages that apply to the request.
     *
     * @return array<string, string>
     */

     public function messages(): array
    {
        return [
            'name.required' => [
                'en' => 'Name is required.',
                'kr' => '이름은 필수 항목입니다.',
            ],
            'name.string' => [
                'en' => 'Name must be a string.',
                'kr' => '이름은 문자열이어야 합니다.',
            ],
            'name.max' => [
                'en' => 'Name must be less than or equal to :max characters.',
                'kr' => '이름은 :max자 이하여야 합니다.',
            ],
            'logo.image' => [
                'en' => 'Logo must be a valid image.',
                'kr' => '로고는 유효한 이미지여야 합니다.',
            ],
            'logo.mimes' => [
                'en' => 'Logo must be a file of type: jpeg, png, jpg, gif.',
                'kr' => '로고는 jpeg, png, jpg, gif 형식의 파일이어야 합니다.',
            ],
            'logo.max' => [
                'en' => 'Logo must not be larger than :max kilobytes.',
                'kr' => '로고는 :max킬로바이트를 초과할 수 없습니다.',
            ],
            'remarks.string' => [
                'en' => 'Remarks must be a string.',
                'kr' => '비고는 문자열이어야 합니다.',
            ],
            'status.string' => [
                'en' => 'Status must be a string.',
                'kr' => '상태는 문자열이어야 합니다.',
            ],
            'status.in' => [
                'en' => 'Status must be either Active or Inactive.',
                'kr' => '상태는 Active 또는 Inactive여야 합니다.',
            ],
        ];
    }



    public function validatedData() {
        $validatedData = $this->only([
            'name',
            'logo',
            'remarks',
            'status',
        ]);

        if ($this->hasFile('logo')) {
            $validatedData['logo'] = $this->imageUpload($this, 'logo', 'payment_gateway');
        }
        if (empty($validatedData['status'])) {
            $validatedData['status'] = 'Active';
        }
        return $validatedData;
    }
    
}
