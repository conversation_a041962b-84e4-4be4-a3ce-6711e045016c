<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

use App\Models\User;

class MakeRequestStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        $rules = [
            'parent_id' => 'nullable|exists:contract_requests,id',
            'contract_name' => 'required|string|max:255',
            'contract_details' => 'nullable|string',
            'page_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|in:New Lead,Development,Maintenance,Ended',
            'amount' => 'nullable|numeric|min:0',
            'rfp_files' => 'nullable|array',
            'rfp_files.*' => 'file|mimes:pdf,doc,docx|max:2048',
            'rfp_images' => 'nullable|array',
            'rfp_images.*' => 'image|mimes:jpeg,png,jpg,gif,svg',
        ];

        // If user is admin, customer_id is required
        if (auth()->user()->hasRole('admin') || auth()->user()->hasRole('bacbon')) {
            $rules['customer_id'] = 'required|exists:users,username';
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '계약 이름은 필수 항목입니다.',
            ],
            'contract_name.string' => [
                'en' => 'Contract name should be a string.',
                'kr' => '계약 이름은 문자열이어야 합니다.',
            ],
            'contract_name.max' => [
                'en' => 'Contract name should not exceed 255 characters.',
                'kr' => '계약 이름은 255자를 초과할 수 없습니다.',
            ],
            'contract_details.string' => [
                'en' => 'Contract details should be a string.',
                'kr' => '계약 세부 정보는 문자열이어야 합니다.',
            ],
            'page_url.url' => [
                'en' => 'Page URL should be a valid URL.',
                'kr' => '페이지 URL은 유효한 URL이어야 합니다.',
            ],
            'start_date.date' => [
                'en' => 'Start date should be a valid date.',
                'kr' => '시작 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.date' => [
                'en' => 'End date should be a valid date.',
                'kr' => '종료 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.after_or_equal' => [
                'en' => 'End date should be after or equal to the start date.',
                'kr' => '종료 날짜는 시작 날짜 이후 또는 같아야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: New Lead, Development, Maintenance, Ended.',
                'kr' => '상태는 다음 값 중 하나여야 합니다: New Lead, Development, Maintenance, Ended.',
            ],
        ];
    }

    /**
     * Extract only the required validated fields.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validatedData = $this->only([
            'contract_name',
            'contract_details',
            'page_url',
            'start_date',
            'end_date',
            'status',
            'amount',
        ]);

        // Set default status if not provided
        $validatedData['status'] = $validatedData['status'] ?? 'New Lead';

        // Handle customer_id based on user role
        if (auth()->user()->hasRole('admin') || auth()->user()->hasRole('bacbon')) {
            // For admin/bacbon, get customer_id from request
            $customer = User::where('username', $this->customer_id)->first();
            if ($customer) {
                $validatedData['customer_id'] = $customer->id;
            }
        } else {
            // For visitor, set customer_id to the current user's ID
            $validatedData['customer_id'] = auth()->id();
        }

        // unique_id will be generated in the ContractService

        // Process file uploads
        $files = [];
        $images = [];

        if ($this->hasFile('rfp_files')) {
            $count = 0;
            foreach ($this->file('rfp_files') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_files');
                $file->move($destinationPath, $filename);

                $files[] = 'rfp_files/' . $filename;
            }
        }

        if ($this->hasFile('rfp_images')) {
            $count = 0;
            foreach ($this->file('rfp_images') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_images');
                $file->move($destinationPath, $filename);

                $images[] = 'rfp_images/' . $filename;
            }
        }

        $validatedData['is_development_request'] = $validatedData['parent_id'] != null;

        return [
            'contract' => $validatedData,
            'rfp_files' => $files,
            'rfp_images' => $images
        ];
    }
}
