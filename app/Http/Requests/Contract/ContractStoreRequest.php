<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

use App\Models\User;

class ContractStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        $rules = [
            // Basic contract info
            'request_type' => 'nullable|string|max:100',
            'contract_type_id' => 'nullable|exists:penguin_contract_types,id',
            'contract_name' => 'required|string|max:255',
            'contract_details' => 'nullable|string',
            'page_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|in:New Lead,Development,Maintenance,Ended,Pending',
            'amount' => 'required|numeric|min:0',

            // Auto-renewal and subscription
            'auto_renewal' => 'nullable|boolean',
            'custom_construction_cost' => 'nullable|numeric|min:0',
            'custom_subscription_fee' => 'nullable|numeric|min:0',

            // Admin field (if admin user)
            'customer_id' => 'nullable|string',

            // Development items
            'development_items' => 'nullable|string',
            'has_support_request' => 'nullable|boolean',

            // Files
            'rfp_files' => 'nullable|array',
            'rfp_files.*' => 'file|mimes:pdf,doc,docx|max:2048',
            'rfp_images' => 'nullable|array',
            'rfp_images.*' => 'image|mimes:jpeg,png,jpg,gif,svg',

        ];

        // If user is admin, customer_id is required
        if (auth()->user()->hasRole('admin') || auth()->user()->hasRole('bacbon')) {
            $rules['customer_id'] = 'required|exists:users,username';
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '계약 이름은 필수 항목입니다.',
            ],
            'contract_name.string' => [
                'en' => 'Contract name should be a string.',
                'kr' => '계약 이름은 문자열이어야 합니다.',
            ],
            'contract_name.max' => [
                'en' => 'Contract name should not exceed 255 characters.',
                'kr' => '계약 이름은 255자를 초과할 수 없습니다.',
            ],
            'contract_details.string' => [
                'en' => 'Contract details should be a string.',
                'kr' => '계약 세부 정보는 문자열이어야 합니다.',
            ],
            'page_url.url' => [
                'en' => 'Page URL should be a valid URL.',
                'kr' => '페이지 URL은 유효한 URL이어야 합니다.',
            ],
            'start_date.date' => [
                'en' => 'Start date should be a valid date.',
                'kr' => '시작 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.date' => [
                'en' => 'End date should be a valid date.',
                'kr' => '종료 날짜는 유효한 날짜여야 합니다.',
            ],
            'end_date.after_or_equal' => [
                'en' => 'End date should be after or equal to the start date.',
                'kr' => '종료 날짜는 시작 날짜 이후 또는 같아야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: New Lead, Development, Maintenance, Ended.',
                'kr' => '상태는 다음 값 중 하나여야 합니다: New Lead, Development, Maintenance, Ended.',
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        if ($this->has('auto_renewal') && is_string($this->auto_renewal)) {
            $this->merge([
                'auto_renewal' => filter_var($this->auto_renewal, FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('has_support_request') && is_string($this->has_support_request)) {
            $this->merge([
                'has_support_request' => filter_var($this->has_support_request, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }

    /**
     * Extract only the required validated fields.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validatedData = $this->validated();

        // Handle customer_id based on user role
        $customerId = null;
        if (auth()->user()->hasRole('admin') && !empty($validatedData['customer_id'])) {
            // For admin, get customer_id from request (username)
            $customer = User::where('username', $validatedData['customer_id'])->first();
            if ($customer) {
                $customerId = $customer->id;
            }
        } else {
            // For visitor, set customer_id to the current user's ID
            $customerId = auth()->id();
        }

        // Prepare contract data
        $contractData = [
            'customer_id' => $customerId,
            'contract_type_id' => $validatedData['contract_type_id'] ?? null,
            'contract_name' => $validatedData['contract_name'],
            'contract_details' => $validatedData['contract_details'] ?? null,
            'amount' => $validatedData['amount'],
            'start_date' => $validatedData['start_date'] ?? null,
            'end_date' => $validatedData['end_date'] ?? null,
            'page_url' => $validatedData['page_url'] ?? null,
            'status' => $validatedData['status'] ?? 'New Lead',
            'base_construction_cost' => $validatedData['custom_construction_cost'] ?? 0,
            'monthly_subscription_fee' => $validatedData['custom_subscription_fee'] ?? 0,
            'created_by' => auth()->id(),
        ];

        // Parse development items
        $developmentItems = [];
        if (!empty($validatedData['development_items'])) {
            $developmentItems = json_decode($validatedData['development_items'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $developmentItems = [];
            }
        }

        // Process file uploads
        $files = [];
        $images = [];

        if ($this->hasFile('rfp_files')) {
            $count = 0;
            foreach ($this->file('rfp_files') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_files');
                $file->move($destinationPath, $filename);

                $files[] = 'rfp_files/' . $filename;
            }
        }

        if ($this->hasFile('rfp_images')) {
            $count = 0;
            foreach ($this->file('rfp_images') as $file) {
                $filename = time(). $count++ . '_' . $file->getClientOriginalName();
                $destinationPath = public_path('uploads/rfp_images');
                $file->move($destinationPath, $filename);

                $images[] = 'rfp_images/' . $filename;
            }
        }

        return [
            'contract' => $contractData,
            'development_items' => $developmentItems,
            'rfp_files' => $files,
            'rfp_images' => $images,
            'auto_renewal' => $validatedData['auto_renewal'] ?? false,
            'custom_subscription_fee' => $validatedData['custom_subscription_fee'] ?? null,
            'has_support_request' => $validatedData['has_support_request'] ?? false,
        ];
    }
}
