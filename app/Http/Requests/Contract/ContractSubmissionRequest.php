<?php

namespace App\Http\Requests\Contract;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\User;

class ContractSubmissionRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Basic contract info
            'request_type' => 'required|string|max:100',
            'contract_type_id' => 'nullable|exists:penguin_contract_types,id',
            'contract_name' => 'required|string|max:255',
            'contract_details' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            
            // Auto-renewal and subscription
            'auto_renewal' => 'nullable|boolean',
            'custom_construction_cost' => 'nullable|numeric|min:0',
            'custom_subscription_fee' => 'nullable|numeric|min:0',
            
            // Admin field (if admin user)
            'customer_id' => 'nullable|string',
            
            // Development items
            'development_items' => 'nullable|string', // JSON string
            'has_support_request' => 'nullable|boolean',
            
            // Files
            'rfp_files' => 'nullable|array',
            'rfp_files.*' => 'file|mimes:pdf,doc,docx|max:2048',
            'rfp_images' => 'nullable|array',
            'rfp_images.*' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            
            // Additional fields
            'page_url' => 'nullable|url',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'request_type.required' => [
                'en' => 'Request type is required.',
                'kr' => '요청 유형이 필요합니다.',
            ],
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '계약 이름이 필요합니다.',
            ],
            'amount.required' => [
                'en' => 'Amount is required.',
                'kr' => '금액이 필요합니다.',
            ],
            'amount.numeric' => [
                'en' => 'Amount must be a valid number.',
                'kr' => '금액은 유효한 숫자여야 합니다.',
            ],
            'start_date.required' => [
                'en' => 'Start date is required.',
                'kr' => '시작 날짜가 필요합니다.',
            ],
            'start_date.date' => [
                'en' => 'Start date must be a valid date.',
                'kr' => '시작 날짜는 유효한 날짜여야 합니다.',
            ],
            'development_items.string' => [
                'en' => 'Development items must be a valid JSON string.',
                'kr' => '개발 항목은 유효한 JSON 문자열이어야 합니다.',
            ],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        if ($this->has('auto_renewal') && is_string($this->auto_renewal)) {
            $this->merge([
                'auto_renewal' => filter_var($this->auto_renewal, FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('has_support_request') && is_string($this->has_support_request)) {
            $this->merge([
                'has_support_request' => filter_var($this->has_support_request, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }

    /**
     * Get the validated data from the request.
     *
     * @return array
     */
    public function validatedData(): array
    {
        $validatedData = $this->validated();
        
        // Handle customer_id based on user role
        $customerId = null;
        if (auth()->user()->hasRole('admin') && !empty($validatedData['customer_id'])) {
            // For admin, get customer_id from request (username)
            $customer = User::where('username', $validatedData['customer_id'])->first();
            if ($customer) {
                $customerId = $customer->id;
            }
        } else {
            // For visitor, set customer_id to the current user's ID
            $customerId = auth()->id();
        }

        // Prepare contract data
        $contractData = [
            'customer_id' => $customerId,
            'contract_type_id' => $validatedData['contract_type_id'] ?? null,
            'contract_name' => $validatedData['contract_name'],
            'contract_details' => $validatedData['contract_details'] ?? null,
            'amount' => $validatedData['amount'],
            'start_date' => $validatedData['start_date'],
            'end_date' => $validatedData['end_date'] ?? null,
            'page_url' => $validatedData['page_url'] ?? null,
            'status' => 'Pending',
            'base_construction_cost' => $validatedData['custom_construction_cost'] ?? 0,
            'monthly_subscription_fee' => $validatedData['custom_subscription_fee'] ?? 0,
            'created_by' => auth()->id(),
        ];

        // Parse development items
        $developmentItems = [];
        if (!empty($validatedData['development_items'])) {
            $developmentItems = json_decode($validatedData['development_items'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $developmentItems = [];
            }
        }

        // Prepare files and images
        $rfpFiles = [];
        if ($this->hasFile('rfp_files')) {
            foreach ($this->file('rfp_files') as $file) {
                $path = $file->store('rfp_files', 'public');
                $rfpFiles[] = $path;
            }
        }

        $rfpImages = [];
        if ($this->hasFile('rfp_images')) {
            foreach ($this->file('rfp_images') as $image) {
                $path = $image->store('rfp_images', 'public');
                $rfpImages[] = $path;
            }
        }

        return [
            'contract' => $contractData,
            'development_items' => $developmentItems,
            'rfp_files' => $rfpFiles,
            'rfp_images' => $rfpImages,
            'auto_renewal' => $validatedData['auto_renewal'] ?? false,
            'custom_subscription_fee' => $validatedData['custom_subscription_fee'] ?? null,
            'has_support_request' => $validatedData['has_support_request'] ?? false,
        ];
    }
}
