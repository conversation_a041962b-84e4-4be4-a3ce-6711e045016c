<?php

namespace App\Http\Requests\Contract;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class AdminCompleteContractRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can complete contracts
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'comment' => 'required|string|max:500',
            'files' => 'nullable|array',
            'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'comment.required' => [
                'en' => 'A comment is required when completing a contract.',
                'kr' => '계약을 완료할 때 코멘트가 필요합니다.',
            ],
            'comment.max' => [
                'en' => 'Comment should not exceed 500 characters.',
                'kr' => '코멘트는 500자를 초과할 수 없습니다.',
            ],
            'files.*.mimes' => [
                'en' => 'Files should be in pdf, doc, docx, jpg, jpeg, or png format.',
                'kr' => '파일은 pdf, doc, docx, jpg, jpeg 또는 png 형식이어야 합니다.',
            ],
            'files.*.max' => [
                'en' => 'Files should not exceed 2MB.',
                'kr' => '파일은 2MB를 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $data = [
            'comment' => $this->comment,
            'files' => [],
        ];

        // Process file uploads
        if ($this->hasFile('files')) {
            foreach ($this->file('files') as $file) {
                try {
                    // Get file information before moving it
                    $originalName = $file->getClientOriginalName();
                    $mimeType = $file->getClientMimeType();
                    $size = $file->getSize();

                    // Generate filename and move file
                    $filename = time() . '_' . $originalName;
                    $destinationPath = public_path('uploads/admin_contract_completion');

                    // Create directory if it doesn't exist
                    if (!file_exists($destinationPath)) {
                        mkdir($destinationPath, 0755, true);
                    }

                    $file->move($destinationPath, $filename);
                    $filePath = 'admin_contract_completion/' . $filename;

                    $data['files'][] = [
                        'path' => $filePath,
                        'original_name' => $originalName,
                        'type' => $mimeType,
                        'size' => $size,
                    ];
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Log::error('Error uploading file: ' . $e->getMessage());
                }
            }
        }

        return $data;
    }
}
