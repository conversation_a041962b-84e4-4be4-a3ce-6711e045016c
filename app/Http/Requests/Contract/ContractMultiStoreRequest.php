<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class ContractMultiStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.name' => 'required|string|max:255',
            '*.email' => 'required|email|unique:contracts,email',
            '*.phone' => 'required|string|max:20',
        ];
    }

    /**
     * Get the custom validation messages for rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            '*.name.required' => [
                'en' => 'Name is required for all contracts.',
                'kr' => '모든 계약에 이름이 필요합니다.',
            ],
            '*.name.string' => [
                'en' => 'Name should be a valid string for all contracts.',
                'kr' => '모든 계약의 이름은 유효한 문자열이어야 합니다.',
            ],
            '*.name.max' => [
                'en' => 'Name should not exceed 255 characters for any contract.',
                'kr' => '모든 계약의 이름은 255자를 초과할 수 없습니다.',
            ],
            '*.email.required' => [
                'en' => 'Email is required for all contracts.',
                'kr' => '모든 계약에 이메일이 필요합니다.',
            ],
            '*.email.email' => [
                'en' => 'Email should be a valid email address for all contracts.',
                'kr' => '모든 계약의 이메일은 유효한 이메일 주소여야 합니다.',
            ],
            '*.email.unique' => [
                'en' => 'Email must be unique for all contracts.',
                'kr' => '모든 계약의 이메일은 고유해야 합니다.',
            ],
            '*.phone.required' => [
                'en' => 'Phone number is required for all contracts.',
                'kr' => '모든 계약에 전화번호가 필요합니다.',
            ],
            '*.phone.string' => [
                'en' => 'Phone number should be a valid string for all contracts.',
                'kr' => '모든 계약의 전화번호는 유효한 문자열이어야 합니다.',
            ],
            '*.phone.max' => [
                'en' => 'Phone number should not exceed 20 characters for any contract.',
                'kr' => '모든 계약의 전화번호는 20자를 초과할 수 없습니다.',
            ],
        ];
    }

    /**
     * Process and generate the validated data.
     *
     * @return array<mixed>
     */
    public function validatedData(): array
    {
        return array_map(function ($contract) {
            return [
                'name' => ucfirst($contract['name']),
                'email' => strtolower($contract['email']),
                'phone' => preg_replace('/[^0-9]/', '', $contract['phone']), // Remove non-numeric characters
            ];
        }, $this->validated());
    }
}
