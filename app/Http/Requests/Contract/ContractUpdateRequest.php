<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Trait\HelperTrait;

class ContractUpdateRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
        public function rules(): array
        {
            return [
                'parent_id' => 'sometimes|required|exists:penguin_contracts,id',
                'contract_name' => 'required|string|max:255',
                'contract_details' => 'nullable|string|max:2000',
                'page_url' => 'nullable|url|max:255',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'status' => ['nullable', Rule::in(['New Lead', 'Development', 'Maintenance', 'Completed'])],
                'amount' => 'nullable|numeric|min:0|max:9999999999.99',

                'rfp_images' => 'nullable|array',
                // 'rfp_images.*' => 'sometimes|required|mimes:jpeg,png,jpg,gif,svg|max:2048|string',
                
                'rfp_images.*' => [
                    'nullable',
                    function ($attribute, $value, $fail) {
                        if ($value instanceof \Illuminate\Http\UploadedFile) {
                            // Validate file mime type and size manually
                            $allowedMimes = ['jpeg', 'png', 'jpg', 'gif', 'svg'];
                            if (!in_array($value->getClientOriginalExtension(), $allowedMimes)) {
                                $fail("The $attribute must be a valid document file.");
                            }
                            if ($value->getSize() > 5 * 1024 * 1024) {
                                $fail("The $attribute may not be greater than 5MB.");
                            }
                        } elseif (!is_string($value)) {
                            $fail("The $attribute must be a file or a valid file path string.");
                        }
                    },
                ],

                'rfp_files' => 'nullable|array',
                'rfp_files.*' => [
                    'nullable',
                    function ($attribute, $value, $fail) {
                        if ($value instanceof \Illuminate\Http\UploadedFile) {
                            // Validate file mime type and size manually
                            $allowedMimes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
                            if (!in_array($value->getClientOriginalExtension(), $allowedMimes)) {
                                $fail("The $attribute must be a valid document file.");
                            }
                            if ($value->getSize() > 5 * 1024 * 1024) {
                                $fail("The $attribute may not be greater than 5MB.");
                            }
                        } elseif (!is_string($value)) {
                            $fail("The $attribute must be a file or a valid file path string.");
                        }
                    },
                ],

                'development_items' => 'nullable|string',
                'additional_items_total' => 'nullable|numeric|min:0',
            ];
        }


    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'parent_id.required' => 'The parent contract is required.',
            'parent_id.exists' => 'The selected parent contract is invalid.',
            
            'contract_name.required' => 'The contract name is required.',
            'contract_name.string' => 'The contract name must be a string.',
            'contract_name.max' => 'The contract name may not be greater than 255 characters.',
            
            'contract_details.string' => 'The contract details must be a string.',
            'contract_details.max' => 'The contract details may not be greater than 2000 characters.',
            
            'page_url.url' => 'The page URL must be a valid URL.',
            'page_url.max' => 'The page URL may not be greater than 255 characters.',
            
            'start_date.date' => 'The start date must be a valid date.',
            
            'end_date.date' => 'The end date must be a valid date.',
            'end_date.after_or_equal' => 'The end date must be after or equal to the start date.',
            
            'status.required' => 'The status is required.',
            'status.in' => 'The selected status is invalid.',
            
            'amount.numeric' => 'The amount must be a number.',
            'amount.min' => 'The amount must be at least 0.',
            'amount.max' => 'The amount may not be greater than 9999999999.99.',
            
            'rfp_images.array' => 'The RFP images must be an array.',
            'rfp_images.*.image' => 'Each RFP image must be an image file.',
            'rfp_images.*.mimes' => 'Each RFP image must be a file of type: jpeg, png, jpg, gif, svg.',
            'rfp_images.*.max' => 'Each RFP image may not be greater than 2048 kilobytes.',
            
            'rfp_files.array' => 'The RFP files must be an array.',
            'rfp_files.*.file' => 'Each RFP file must be a file.',
            'rfp_files.*.mimes' => 'Each RFP file must be a file of type: pdf, doc, docx, xls, xlsx, ppt, pptx.',
            'rfp_files.*.max' => 'Each RFP file may not be greater than 5120 kilobytes.',
        
            
            'additional_items_total.numeric' => 'The additional items total must be a number.',
            'additional_items_total.min' => 'The additional items total must be at least 0.',
        ];
    }


    /**
     * Handle a passed validation attempt.
     */

    /**
     * Get the validated data from the request.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validated = $this->validated();

        $data = [
            'contract' => [
                'parent_id' => $validated['parent_id'] ?? null,
                'contract_name' => $validated['contract_name'],
                'contract_details' => $validated['contract_details'] ?? null,
                'page_url' => $validated['page_url'] ?? null,
                'start_date' => $validated['start_date'] ?? null,
                'end_date' => $validated['end_date'] ?? null,
                'amount' => $validated['amount'] ?? null,
                'additional_items_total' => $validated['additional_items_total'] ?? 0,
            ],
        ];

                // Parse development items
        $developmentItems = [];
        if (!empty($validated['development_items'])) {
            $developmentItems = json_decode($validated['development_items'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $developmentItems = [];
            }
        }

        $data['development_items'] = $developmentItems;

        if (isset($validated['status'])) {
            $data['status'] = $validated['status'];
        } 
        // Handle file uploads
        if ($this->hasFile('rfp_files')) {
            $data['rfp_files'] = $this->handleFileUploads('rfp_files', 'contracts/files');
        }

        if ($this->hasFile('rfp_images')) {
            $data['rfp_images'] = $this->handleFileUploads('rfp_images', 'contracts/images');
        }

        return $data;
    }

    /**
     * Handle file uploads and return array of file paths.
     */
    protected function handleFileUploads(string $field, string $storagePath): array
    {
        $paths = [];
        foreach ($this->file($field) as $file) {
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs($storagePath, $filename, 'public');
            $paths[] = $path;
        }
        return $paths;
    }
}