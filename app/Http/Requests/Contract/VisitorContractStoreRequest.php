<?php

namespace App\Http\Requests\Contract;

use App\Models\User;
use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class VisitorContractStoreRequest extends FormRequest
{
    use HelperTrait;
    
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only visitors can create contracts
        return auth()->check() && auth()->user()->hasRole('visitor');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'parent_id' => 'required|exists:penguin_contracts,id',
            'contract_name' => 'required|string|max:255',
            'contract_details' => 'nullable|string',
            'page_url' => 'nullable|url',
            'rfp_files' => 'nullable|array',
            'rfp_files.*' => 'file|mimes:pdf,doc,docx|max:2048',
            'rfp_images' => 'nullable|array',
            'rfp_images.*' => 'image|mimes:jpeg,png,jpg,gif,svg',
            'development_items' => 'nullable|string',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, array<string, string>>
     */
    public function messages(): array
    {
        return [
            'parent_id.required' => [
                'en' => 'Parent contract is required.',
                'kr' => '상위 계약이 필요합니다.',
            ],
            'parent_id.exists' => [
                'en' => 'Selected parent contract does not exist.',
                'kr' => '선택한 상위 계약이 존재하지 않습니다.',
            ],
            'contract_name.required' => [
                'en' => 'Contract name is required.',
                'kr' => '계약 이름이 필요합니다.',
            ],
            'contract_name.max' => [
                'en' => 'Contract name cannot exceed 255 characters.',
                'kr' => '계약 이름은 255자를 초과할 수 없습니다.',
            ],
            'page_url.url' => [
                'en' => 'Page URL must be a valid URL.',
                'kr' => '페이지 URL은 유효한 URL이어야 합니다.',
            ],
            'rfp_files.*.mimes' => [
                'en' => 'Files must be of type: pdf, doc, docx.',
                'kr' => '파일은 pdf, doc, docx 유형이어야 합니다.',
            ],
            'rfp_files.*.max' => [
                'en' => 'Files may not be greater than 2MB.',
                'kr' => '파일은 2MB를 초과할 수 없습니다.',
            ],
            'rfp_images.*.image' => [
                'en' => 'Images must be valid image files.',
                'kr' => '이미지는 유효한 이미지 파일이어야 합니다.',
            ],
            'rfp_images.*.mimes' => [
                'en' => 'Images must be of type: jpeg, png, jpg, gif, svg.',
                'kr' => '이미지는 jpeg, png, jpg, gif, svg 유형이어야 합니다.',
            ],
        ];
    }

    /**
     * Get the validated data from the request.
     *
     * @return array
     */
    public function validatedData(): array
    {
        $validatedData = $this->validated();
        
        // Prepare contract data
        $contractData = [
            'parent_id' => $validatedData['parent_id'],
            'contract_name' => $validatedData['contract_name'],
            'contract_details' => $validatedData['contract_details'] ?? null,
            'page_url' => $validatedData['page_url'] ?? null,
            'status' => 'Pending', // Always set status to Pending for visitor contracts
            'customer_id' => auth()->id(), // Set customer_id to the current visitor's ID
        ];
                // Parse development items
        $developmentItems = [];
        if (!empty($validatedData['development_items'])) {
            $developmentItems = json_decode($validatedData['development_items'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $developmentItems = [];
            }
        }

        // Prepare files and images if they exist
        $rfpFiles = [];
        if ($this->hasFile('rfp_files')) {
            foreach ($this->file('rfp_files') as $file) {
                $path = $file->store('rfp_files', 'public');
                $rfpFiles[] = $path;
            }
        }
        
        $rfpImages = [];
        if ($this->hasFile('rfp_images')) {
            foreach ($this->file('rfp_images') as $image) {
                $path = $image->store('rfp_images', 'public');
                $rfpImages[] = $path;
            }
        }
        
        return [
            'contract' => $contractData,
            'rfp_files' => $rfpFiles,
            'rfp_images' => $rfpImages,
            'development_items' => $developmentItems,
        ];
    }
}
