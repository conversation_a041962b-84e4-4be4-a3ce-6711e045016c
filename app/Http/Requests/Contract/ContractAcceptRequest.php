<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class ContractAcceptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow visitors to accept contracts
        return auth()->check() && auth()->user()->hasRole('visitor');
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'comment' => $this->comment ? trim($this->comment) : null,
            'payment_type' => $this->payment_type ? trim($this->payment_type) : null,
            'remarks' => $this->remarks ? trim($this->remarks) : null,
            'payment_gateway_name' => $this->payment_gateway_name ? trim($this->payment_gateway_name) : null,
            'payment_gateway_method_name' => $this->payment_gateway_method_name ? trim($this->payment_gateway_method_name) : null,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'comment' => 'nullable|string|max:500',
            'files' => 'nullable|array',
            'files.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,bmp,svg,webp,tiff,tif,ico,heic,heif|max:2048',
            'paid_amount' => 'nullable|numeric|min:0',
            'payment_date' => 'nullable|date',
            'payment_type' => 'nullable|string|max:255',
            'remarks' => 'nullable|string',
            'payment_gateway_id' => 'nullable|exists:penguin_payment_gateways,id',
            'payment_gateway_method_id' => 'nullable|exists:penguin_payment_gateway_methods,id',
            'payment_gateway_name' => 'nullable|string|max:255',
            'payment_gateway_method_name' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'comment.max' => [
                'en' => 'Comment should not exceed 500 characters.',
                'kr' => '댓글은 500자를 초과할 수 없습니다.',
            ],
            'files.*.mimes' => [
                'en' => 'Files should be in pdf, doc, docx, jpg, jpeg, or png format.',
                'kr' => '파일은 pdf, doc, docx, jpg, jpeg 또는 png 형식이어야 합니다.',
            ],
            'files.*.max' => [
                'en' => 'Files should not exceed 2MB.',
                'kr' => '파일은 2MB를 초과할 수 없습니다.',
            ],
            'paid_amount.numeric' => [
                'en' => 'The paid amount must be a number.',
                'kr' => '지불 금액은 숫자여야 합니다.',
            ],
            'paid_amount.min' => [
                'en' => 'The paid amount must be at least 0.',
                'kr' => '지불 금액은 최소 0이어야 합니다.',
            ],
            'payment_date.date' => [
                'en' => 'The payment date must be a valid date.',
                'kr' => '결제일은 유효한 날짜여야 합니다.',
            ],
            'payment_gateway_id.exists' => [
                'en' => 'The selected payment gateway does not exist.',
                'kr' => '선택한 결제 게이트웨이가 존재하지 않습니다.',
            ],
            'payment_gateway_method_id.exists' => [
                'en' => 'The selected payment method does not exist.',
                'kr' => '선택한 결제 방법이 존재하지 않습니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $data = [
            'comment' => $this->comment,
            'files' => [],
            'payment' => null,
        ];

        // Process file uploads
        if ($this->hasFile('files')) {
            foreach ($this->file('files') as $file) {
                try {
                    $originalName = $file->getClientOriginalName();
                    $mimeType = $file->getClientMimeType();
                    $size = $file->getSize();

                    $filename = time() . '_' . $originalName;
                    $destinationPath = public_path('uploads/contract_acceptance');

                    if (!file_exists($destinationPath)) {
                        mkdir($destinationPath, 0755, true);
                    }

                    $file->move($destinationPath, $filename);
                    $filePath = 'contract_acceptance/' . $filename;

                    $data['files'][] = [
                        'path' => $filePath,
                        'original_name' => $originalName,
                        'type' => $mimeType,
                        'size' => $size,
                    ];
                } catch (\Exception $e) {
                    \Log::error('Error processing file upload: ' . $e->getMessage());
                }
            }
        }

        // Process payment information if provided
        if (
            $this->filled('paid_amount') || $this->filled('payment_date') || $this->filled('payment_type') ||
            $this->filled('remarks') || $this->filled('payment_gateway_id') || $this->filled('payment_gateway_method_id') ||
            $this->filled('payment_gateway_name') || $this->filled('payment_gateway_method_name')
        ) {
            $payment = [
                'paid_amount' => $this->input('paid_amount'),
                'payment_date' => $this->input('payment_date'),
                'payment_type' => $this->input('payment_type'),
                'remarks' => $this->input('remarks'),
                'payment_gateway_id' => $this->input('payment_gateway_id'),
                'payment_gateway_method_id' => $this->input('payment_gateway_method_id'),
                'payment_gateway_name' => $this->input('payment_gateway_name'),
                'payment_gateway_method_name' => $this->input('payment_gateway_method_name'),
            ];

            // Fetch gateway names if IDs are given but names are missing
            if (!empty($payment['payment_gateway_id']) && empty($payment['payment_gateway_name'])) {
                $paymentGateway = \App\Models\PaymentGateway::find($payment['payment_gateway_id']);
                if ($paymentGateway) {
                    $payment['payment_gateway_name'] = $paymentGateway->name;
                }
            }

            if (!empty($payment['payment_gateway_method_id']) && empty($payment['payment_gateway_method_name'])) {
                $paymentGatewayMethod = \App\Models\PaymentGatewayMethod::find($payment['payment_gateway_method_id']);
                if ($paymentGatewayMethod) {
                    $payment['payment_gateway_method_name'] = $paymentGatewayMethod->method_name;
                }
            }

            // Filter out null values
            $payment = array_filter($payment, fn($value) => !is_null($value));

            $data['payment'] = empty($payment) ? null : $payment;
        }

        return $data;
    }
}
