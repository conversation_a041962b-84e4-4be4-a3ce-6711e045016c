<?php

namespace App\Http\Requests;

use App\Models\SupportRequest;
use App\Services\SupportRequestService;
use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SupportRequestUpdateRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $supportRequest = $this->route('id')
            ? SupportRequest::find($this->route('id'))
            : null;

        if (!$supportRequest) {
            return false;
        }

        // Use the service to check if the user can access this request
        $service = app(SupportRequestService::class);
        return $service->canAccess($supportRequest);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'request_type' => ['sometimes', 'string', 'max:255'],
            'title' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string'],
            'status' => ['sometimes', 'string', 'in:New,In Progress,Resolved,Closed'],
            'files' => ['nullable', 'array'],
            'files.*' => ['file', 'mimes:pdf,doc,docx,txt,jpeg,png,jpg,gif', 'max:2048'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'request_type.string' => [
                'en' => 'Request type must be a string.',
                'kr' => '요청 유형은 문자열이어야 합니다.',
            ],
            'title.string' => [
                'en' => 'Title must be a string.',
                'kr' => '제목은 문자열이어야 합니다.',
            ],
            'description.string' => [
                'en' => 'Description must be a string.',
                'kr' => '설명은 문자열이어야 합니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of: New, In Progress, Resolved, Closed.',
                'kr' => '상태는 다음 중 하나여야 합니다: New, In Progress, Resolved, Closed.',
            ],
            'files.*.mimes' => [
                'en' => 'Files must be of type: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
                'kr' => '파일은 다음 유형이어야 합니다: pdf, doc, docx, txt, jpeg, png, jpg, gif.',
            ],
            'files.*.max' => [
                'en' => 'Files may not be larger than 2MB.',
                'kr' => '파일은 2MB보다 클 수 없습니다.',
            ],
        ];
    }

    /**
     * Process the validated data and prepare it for use.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $data = $this->only([
            'request_type',
            'title',
            'description',
            'status',
        ]);

        // Process file uploads
        $files = [];
        if ($this->hasFile('files')) {
            foreach ($this->file('files') as $file) {
                try {
                    // Get file information before moving it
                    $originalName = $file->getClientOriginalName();
                    $mimeType = $file->getClientMimeType();
                    $size = $file->getSize();

                    // Generate filename and move file
                    $filename = time() . '_' . $originalName;
                    $destinationPath = public_path('uploads/support_requests');

                    // Create directory if it doesn't exist
                    if (!file_exists($destinationPath)) {
                        mkdir($destinationPath, 0755, true);
                    }

                    $file->move($destinationPath, $filename);
                    $filePath = 'support_requests/' . $filename;

                    $files[] = [
                        'path' => $filePath,
                        'original_name' => $originalName,
                        'type' => $mimeType,
                        'size' => $size,
                    ];
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Log::error('Error uploading file: ' . $e->getMessage());
                }
            }
        }

        $data['files'] = $files;

        return $data;
    }
}
