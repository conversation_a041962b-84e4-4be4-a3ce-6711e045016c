<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Contract;

class PaymentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'contact_id' => 'required|exists:penguin_contracts,id',
            'paid_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:pending,partial,complete',
            'payment_date' => 'nullable|date',
            'payment_type' => 'nullable|string|max:255',
            'remarks' => 'nullable|string',
            'payment_gateway_id' => 'nullable|exists:penguin_payment_gateways,id',
            'payment_gateway_method_id' => 'nullable|exists:penguin_payment_gateway_methods,id',
            'payment_gateway_name' => 'nullable|string|max:255',
            'payment_gateway_method_name' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contact_id.required' => [
                'en' => 'Contact ID is required.',
                'kr' => '연락처 ID는 필수 항목입니다.',
            ],
            'contact_id.exists' => [
                'en' => 'The selected contact ID does not exist.',
                'kr' => '선택한 연락처 ID가 존재하지 않습니다.',
            ],
            'agreed_payment.required' => [
                'en' => 'The agreed payment is required.',
                'kr' => '합의된 금액은 필수입니다.',
            ],
            'agreed_payment.numeric' => [
                'en' => 'The agreed payment must be a number.',
                'kr' => '합의된 금액은 숫자여야 합니다.',
            ],
            'agreed_payment.min' => [
                'en' => 'The agreed payment must be at least 0.',
                'kr' => '합의된 금액은 0 이상이어야 합니다.',
            ],
            'paid_amount.numeric' => [
                'en' => 'The paid amount must be a number.',
                'kr' => '지불 금액은 숫자여야 합니다.',
            ],
            'paid_amount.min' => [
                'en' => 'The paid amount must be at least 0.',
                'kr' => '지불 금액은 0 이상이어야 합니다.',
            ],
            'status.required' => [
                'en' => 'Status is required.',
                'kr' => '상태는 필수 항목입니다.',
            ],
            'status.in' => [
                'en' => 'Status must be one of the following: pending, partial, complete.',
                'kr' => '상태는 다음 값 중 하나여야 합니다: pending, partial, complete.',
            ],
            'payment_date.date' => [
                'en' => 'Payment date should be a valid date.',
                'kr' => '결제 날짜는 유효한 날짜여야 합니다.',
            ],
            'payment_type.string' => [
                'en' => 'Payment type must be a string.',
                'kr' => '결제 유형은 문자열이어야 합니다.',
            ],
            'payment_type.max' => [
                'en' => 'Payment type must not exceed 255 characters.',
                'kr' => '결제 유형은 255자를 초과할 수 없습니다.',
            ],
            'remarks.string' => [
                'en' => 'Remarks must be a string.',
                'kr' => '비고는 문자열이어야 합니다.',
            ],
        ];
    }

    /**
     * Extract only the required validated fields.
     *
     * @return array<string, mixed>
     */
    public function validatedData()
    {
        $validatedData = $this->only([
            'contact_id',
            'paid_amount',
            'status',
            'payment_date',
            'payment_type',
            'remarks',
            'payment_gateway_id',
            'payment_gateway_method_id',
            'payment_gateway_name',
            'payment_gateway_method_name',
        ]);
        $validatedData['agreed_payment'] = Contract::find($validatedData['contact_id'])->amount;

        if ($validatedData['agreed_payment'] > $validatedData['paid_amount']) {
            $validatedData['status'] = 'partial';
        } elseif ($validatedData['paid_amount'] == 0) {
            $validatedData['status'] = 'pending';
        } else {
            $validatedData['status'] = 'complete';
        }

        // If payment gateway ID is provided but name is not, get the name from the model
        if (!empty($validatedData['payment_gateway_id']) && empty($validatedData['payment_gateway_name'])) {
            $paymentGateway = \App\Models\PaymentGateway::find($validatedData['payment_gateway_id']);
            if ($paymentGateway) {
                $validatedData['payment_gateway_name'] = $paymentGateway->name;
            }
        }

        // If payment gateway method ID is provided but name is not, get the name from the model
        if (!empty($validatedData['payment_gateway_method_id']) && empty($validatedData['payment_gateway_method_name'])) {
            $paymentGatewayMethod = \App\Models\PaymentGatewayMethod::find($validatedData['payment_gateway_method_id']);
            if ($paymentGatewayMethod) {
                $validatedData['payment_gateway_method_name'] = $paymentGatewayMethod->method_name;
            }
        }

        return $validatedData;
    }
}
