<?php

namespace App\Http\Requests\Payment;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class ContractPaymentProcessRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'paid_amount' => 'nullable|numeric|min:0|max:*********.99',
            'payment_date' => 'nullable|date',
            'payment_method' => 'nullable|in:credit_card,bank_transfer,paypal,stripe,manual,cash,other',
            'payment_gateway' => 'nullable|string|max:100',
            'transaction_id' => 'nullable|string|max:255',
            'payment_gateway_id' => 'nullable|exists:penguin_payment_gateways,id',
            'payment_gateway_method_id' => 'nullable|exists:penguin_payment_gateway_methods,id',
            'payment_gateway_name' => 'nullable|string|max:255',
            'payment_gateway_method_name' => 'nullable|string|max:255',
            'remarks' => 'nullable|string|max:1000',
            'payment_details' => 'nullable|array',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'paid_amount.numeric' => [
                'en' => 'Paid amount must be a valid number.',
                'kr' => '지불 금액은 유효한 숫자여야 합니다.',
            ],
            'paid_amount.min' => [
                'en' => 'Paid amount must be greater than or equal to 0.',
                'kr' => '지불 금액은 0 이상이어야 합니다.',
            ],
            'paid_amount.max' => [
                'en' => 'Paid amount cannot exceed 999,999,999.99.',
                'kr' => '지불 금액은 999,999,999.99를 초과할 수 없습니다.',
            ],
            'payment_date.date' => [
                'en' => 'Payment date must be a valid date.',
                'kr' => '결제 날짜는 유효한 날짜여야 합니다.',
            ],
            'payment_method.in' => [
                'en' => 'Payment method must be one of: credit_card, bank_transfer, paypal, stripe, manual, cash, other.',
                'kr' => '결제 방법은 다음 중 하나여야 합니다: credit_card, bank_transfer, paypal, stripe, manual, cash, other.',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'paid_amount' => 'paid amount',
            'payment_date' => 'payment date',
            'payment_method' => 'payment method',
            'payment_gateway' => 'payment gateway',
            'transaction_id' => 'transaction ID',
            'remarks' => 'remarks',
            'payment_details' => 'payment details',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default payment date if not provided
        if (!$this->has('payment_date')) {
            $this->merge(['payment_date' => now()->format('Y-m-d')]);
        }
    }
}
