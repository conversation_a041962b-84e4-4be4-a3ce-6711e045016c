<?php

namespace App\Http\Requests\ContractType;

use App\Trait\HelperTrait;
use Illuminate\Foundation\Http\FormRequest;

class ContractTypeStoreRequest extends FormRequest
{
    use HelperTrait;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can create contract types
        return auth()->check() && auth()->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255|unique:penguin_contract_types,title',
            'short_description' => 'nullable|string|max:500',
            'construction_cost' => 'required|numeric|min:0|max:999999999.99',
            'subscription_fee' => 'required|numeric|min:0|max:999999999.99',
            'category' => 'nullable|string|max:100',
            'slug' => 'nullable|string|max:255|unique:penguin_contract_types,slug',
            'is_active' => 'nullable|boolean',
            'sort_order' => 'nullable|integer|min:0',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'detailed_description' => 'nullable|string|max:2000',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'title.required' => [
                'en' => 'Contract type title is required.',
                'kr' => '계약 유형 제목이 필요합니다.',
            ],
            'title.unique' => [
                'en' => 'Contract type title must be unique.',
                'kr' => '계약 유형 제목은 고유해야 합니다.',
            ],
            'construction_cost.required' => [
                'en' => 'Construction cost is required.',
                'kr' => '구축 비용이 필요합니다.',
            ],
            'construction_cost.numeric' => [
                'en' => 'Construction cost must be a valid number.',
                'kr' => '구축 비용은 유효한 숫자여야 합니다.',
            ],
            'construction_cost.min' => [
                'en' => 'Construction cost cannot be negative.',
                'kr' => '구축 비용은 음수일 수 없습니다.',
            ],
            'subscription_fee.required' => [
                'en' => 'Subscription fee is required.',
                'kr' => '구독료가 필요합니다.',
            ],
            'subscription_fee.numeric' => [
                'en' => 'Subscription fee must be a valid number.',
                'kr' => '구독료는 유효한 숫자여야 합니다.',
            ],
            'subscription_fee.min' => [
                'en' => 'Subscription fee cannot be negative.',
                'kr' => '구독료는 음수일 수 없습니다.',
            ],
            'slug.unique' => [
                'en' => 'Slug must be unique.',
                'kr' => '슬러그는 고유해야 합니다.',
            ],
            'features.array' => [
                'en' => 'Features must be an array.',
                'kr' => '기능은 배열이어야 합니다.',
            ],
            'color.regex' => [
                'en' => 'Color must be a valid hex color code (e.g., #FF0000).',
                'kr' => '색상은 유효한 16진수 색상 코드여야 합니다 (예: #FF0000).',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'title' => 'contract type title',
            'short_description' => 'short description',
            'construction_cost' => 'construction cost',
            'subscription_fee' => 'subscription fee',
            'category' => 'category',
            'slug' => 'slug',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
            'features' => 'features',
            'icon' => 'icon',
            'color' => 'color',
            'detailed_description' => 'detailed description',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('sort_order')) {
            $this->merge(['sort_order' => 0]);
        }

        // Convert string boolean to actual boolean
        if ($this->has('is_active') && is_string($this->is_active)) {
            $this->merge([
                'is_active' => filter_var($this->is_active, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
