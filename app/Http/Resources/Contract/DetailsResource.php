<?php

namespace App\Http\Resources\Contract;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Load relationships if they haven't been loaded yet
        if (!$this->relationLoaded('statusHistory')) {
            $this->load('statusHistory.user');
        }

        if (!$this->relationLoaded('customer')) {
            $this->load('customer:id,name,username,email');
        }

        if (!$this->relationLoaded('creator')) {
            $this->load('creator:id,name,username,email');
        }

        if (!$this->relationLoaded('drafts')) {
            $this->load('drafts.creator', 'drafts.responder', 'drafts.files', 'drafts.fileAttachments', 'drafts.urlAttachments');
        }

        if (!$this->relationLoaded('parentContract')) {
            $this->load('parentContract');
        }

        if (!$this->relationLoaded('childContracts')) {
            $this->load('childContracts');
        }

        return [
            "id" => $this->id,
            "unique_id" => $this->unique_id,
            "contract_name" => $this->contract_name,
            "contract_details" => $this->contract_details,
            "page_url" => $this->page_url,
            "start_date" => $this->start_date,
            "end_date" => $this->end_date,
            "amount" => $this->amount,
            "status" => $this->status,
            "contract_type" => $this->contractType,
            "customer" => $this->customer ? [
                'id' => $this->customer->id,
                'name' => $this->customer->name,
                'username' => $this->customer->username,
                'email' => $this->customer->email,
            ] : null,
            "created_by" => $this->creator ? [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
                'username' => $this->creator->username,
                'email' => $this->creator->email,
            ] : null,
            "payments" => $this->payments,
            'rfp_images' => $this->images,
            'rfp_files' => $this->files,
            'rfp_status' => $this->rfp_status,
            'drafts' => $this->whenLoaded('drafts', function() {
                return $this->drafts->map(function($draft) {
                    // Load relationships if not already loaded
                    if (!$draft->relationLoaded('creator')) {
                        $draft->load('creator:id,name,username,email');
                    }

                    if (!$draft->relationLoaded('responder')) {
                        $draft->load('responder:id,name,username,email');
                    }

                    if (!$draft->relationLoaded('files')) {
                        $draft->load('files');
                    }

                    return [
                        'id' => $draft->id,
                        'contract_id' => $draft->contract_id,
                        'description' => $draft->description,
                        'status' => $draft->status,
                        'version' => $draft->version,
                        'feedback' => $draft->feedback,
                        'creator' => $draft->creator ? [
                            'id' => $draft->creator->id,
                            'name' => $draft->creator->name,
                            'username' => $draft->creator->username,
                            'email' => $draft->creator->email,
                        ] : null,
                        'responder' => $draft->responder ? [
                            'id' => $draft->responder->id,
                            'name' => $draft->responder->name,
                            'username' => $draft->responder->username,
                            'email' => $draft->responder->email,
                        ] : null,
                        'responded_at' => $draft->responded_at,
                        'files' => $draft->files->map(function($file) {
                            return [
                                'id' => $file->id,
                                'type' => $file->type,
                                'file_path' => $file->file_path,
                                'url' => $file->type === 'url' ? $file->url : ($file->file_path ? asset('uploads/' . $file->file_path) : null),
                                'original_filename' => $file->original_filename,
                                'file_type' => $file->file_type,
                                'file_size' => $file->file_size,
                                'created_at' => $file->created_at,
                            ];
                        }),
                        'file_attachments' => $draft->fileAttachments->map(function($file) {
                            return [
                                'id' => $file->id,
                                'file_path' => $file->file_path,
                                'url' => $file->file_path ? asset('uploads/' . $file->file_path) : null,
                                'original_filename' => $file->original_filename,
                                'file_type' => $file->file_type,
                                'file_size' => $file->file_size,
                                'created_at' => $file->created_at,
                            ];
                        }),
                        'url_attachments' => $draft->urlAttachments->map(function($file) {
                            return [
                                'id' => $file->id,
                                'url' => $file->url,
                                'created_at' => $file->created_at,
                            ];
                        }),
                        'created_at' => $draft->created_at,
                        'updated_at' => $draft->updated_at,
                    ];
                });
            }, []),
            'status_history' => $this->whenLoaded('statusHistory', function() {
                return $this->statusHistory->map(function($statusChange) {
                    // Load files relationship if not already loaded
                    if (!$statusChange->relationLoaded('files')) {
                        $statusChange->load('files');
                    }

                    return [
                        'id' => $statusChange->id,
                        'status' => $statusChange->status,
                        'comment' => $statusChange->comment,
                        'file' => $statusChange->file ? asset('uploads/' . $statusChange->file) : null,
                        'files' => $statusChange->files->map(function($file) {
                            return [
                                'id' => $file->id,
                                'file_path' => $file->file_path,
                                'original_filename' => $file->original_filename,
                                'file_type' => $file->file_type,
                                'file_size' => $file->file_size,
                                'url' => $file->url,
                                'created_at' => $file->created_at,
                            ];
                        }),
                        'updated_by' => $statusChange->user ? [
                            'id' => $statusChange->user->id,
                            'name' => $statusChange->user->name,
                            'username' => $statusChange->user->username,
                        ] : null,
                        'created_at' => $statusChange->created_at,
                        'formatted_date' => $statusChange->created_at->format('Y-m-d H:i:s'),
                    ];
                });
            }, []),
            "development_items" => $this->developmentItems,
            "created_at" => $this->created_at,
            "updated_at" => $this->updated_at,
            "parent_contract" => $this->whenLoaded('parentContract', function() {
                return $this->parentContract ? [
                    'id' => $this->parentContract->id,
                    'unique_id' => $this->parentContract->unique_id,
                    'contract_name' => $this->parentContract->contract_name,
                    'status' => $this->parentContract->status,
                ] : null;
            }),
            "child_contracts" => $this->whenLoaded('childContracts', function() {
                return $this->childContracts->map(function($childContract) {
                    return [
                        'id' => $childContract->id,
                        'unique_id' => $childContract->unique_id,
                        'contract_name' => $childContract->contract_name,
                        'status' => $childContract->status,
                        'created_at' => $childContract->created_at,
                    ];
                });
            }, []),
            "subscription" => $this->whenLoaded('subscription', function() {
                return $this->subscription ? [
                    'id' => $this->subscription->id,
                    'subscription_code' => $this->subscription->subscription_code,
                    'monthly_amount' => $this->subscription->monthly_amount,
                    'formatted_amount' => $this->subscription->formatted_amount,
                    'status' => $this->subscription->status,
                    'billing_cycle' => $this->subscription->billing_cycle,
                    'next_billing_date' => $this->subscription->next_billing_date,
                    'auto_renewal' => $this->subscription->auto_renewal,
                    'created_at' => $this->subscription->created_at,
                ] : null;
            })
        ];
    }
}


