<?php

namespace App\Notifications;

use App\Models\ContractDraft;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContractDraftCreated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $draft;
    protected $frontendUrl;

    /**
     * Create a new notification instance.
     */
    public function __construct(ContractDraft $draft)
    {
        $this->draft = $draft;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $contract = $this->draft->contract;
        $creator = $this->draft->creator;
        
        $customerInfo = $contract->customer
            ? $contract->customer->name . ' (' . $contract->customer->email . ')'
            : 'Unknown Customer';

        return (new MailMessage)
            ->subject('New Contract Draft Created: ' . $contract->contract_name)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A new contract draft (version ' . $this->draft->version . ') has been created.')
            ->line('Contract Name: ' . $contract->contract_name)
            ->line('Contract ID: ' . $contract->unique_id)
            ->line('Customer: ' . $customerInfo)
            ->line('Created By: ' . ($creator ? $creator->name : 'Unknown User'))
            ->line('Thank you for using Yellow Penguin!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'draft_id' => $this->draft->id,
            'contract_id' => $this->draft->contract_id,
            'contract_name' => $this->draft->contract->contract_name,
            'version' => $this->draft->version,
            'created_by' => $this->draft->created_by,
        ];
    }
}
