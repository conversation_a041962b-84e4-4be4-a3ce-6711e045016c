<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\URL;
use App\Models\UserOtp;
use App\Trait\HelperTrait;

class ForgotPassword extends Notification
{
    use Queueable, HelperTrait;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($user): MailMessage
    {
        // UserOtp::where('user_id', $user->id)->where('is_used', 0)->delete();

        // $otp = $this->generateOtp(6);
        // $otpData = UserOtp::create([
        //     'user_id' => $user->id,
        //     'otp' => $otp
        // ]);

        // return (new MailMessage)
        //     ->subject('Your OTP for Verification')
        //     ->greeting('Hello,')
        //     ->line('Your Yellow Penguin Verification Code is '.$otp)
        //     ->action('Verify Email Address', $verificationUrl)
        //     ->line('This code will expire in 5 minutes.');



            UserOtp::where('user_id', $user->id)->where('is_used', 0)->delete();

            $secretKey = $this->generateSecretKey(25);
            $otpData = UserOtp::create([
                'user_id' => $user->id,
                'otp' => $secretKey,
                'expired_at' => now()->addMinutes(30)
            ]);

            return (new MailMessage)
            ->subject('Forgot Password')
            ->greeting('Hello,')
            ->line('Follow the link below to reset your account.')
            ->action('Reset Password', 'https://yellowpenguin.co.kr/account/'.$secretKey.'/'.$user->username)
            ->line('This link will expire in 30 minutes.')
            ->line('If you did not request this email, no further action is required.')
            ->line('This is an automated message from EduFSS. Please do not reply to this address.');

    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}

