<?php

namespace App\Notifications;

use App\Models\Contract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContractSentToDevelopment extends Notification implements ShouldQueue
{
    use Queueable;

    protected $contract;
    protected $frontendUrl;
    protected $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(Contract $contract, ?string $comment = null)
    {
        $this->contract = $contract;
        $this->comment = $comment;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $contractUrl = $this->frontendUrl . '/portal/contracts/' . $this->contract->id;

        $customerInfo = $this->contract->customer
            ? $this->contract->customer->name . ' (' . $this->contract->customer->email . ')'
            : 'Unknown Customer';

        $mailMessage = (new MailMessage)
            ->subject('New Contract Ready for Development: ' . $this->contract->contract_name)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A new contract has been sent to development and is ready for your team to start working on it.')
            ->line('Contract Name: ' . $this->contract->contract_name)
            ->line('Contract ID: ' . $this->contract->unique_id)
            ->line('Customer: ' . $customerInfo)
            ->line('Amount: ' . number_format($this->contract->amount, 2));

        if ($this->comment) {
            $mailMessage->line('Admin Comment: ' . $this->comment);
        }

        $mailMessage->action('View Contract', $contractUrl)
            ->line('Please review this contract and begin development as soon as possible.')
            ->line('Thank you for using Yellow Penguin!');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contract_id' => $this->contract->id,
            'contract_name' => $this->contract->contract_name,
            'customer_id' => $this->contract->customer_id,
            'amount' => $this->contract->amount,
            'comment' => $this->comment,
        ];
    }
}
