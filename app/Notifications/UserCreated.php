<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserCreated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $password;
    protected $frontendUrl;
    protected $loginUrl;

    public function __construct($password = null)
    {
        $this->password = $password;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
        $this->loginUrl = 'https://www.yellowpenguin.co.kr/login';
    }

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject('Welcome to ' . config('app.name'))
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Welcome to ' . config('app.name') . '!')
            ->line('Your account has been successfully created.')
            ->line('Your username is: ' . $notifiable->username);

        // Add password if provided
        if ($this->password) {
            $mailMessage->line('Your password is: ' . $this->password);
            $mailMessage->line('Please change your password after your first login for security reasons.');
        }

        $mailMessage->action('Login Now', $this->loginUrl)
            ->line('You can access our website at: ' . $this->frontendUrl)
            ->line('Thank you for joining us!');

        return $mailMessage;
    }
}