<?php

namespace App\Notifications;

use App\Models\Contract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContractPaymentApproved extends Notification implements ShouldQueue
{
    use Queueable;

    protected $contract;
    protected $frontendUrl;

    /**
     * Create a new notification instance.
     */
    public function __construct(Contract $contract)
    {
        $this->contract = $contract;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $contractUrl = $this->frontendUrl . '/portal/contracts/' . $this->contract->id;

        return (new MailMessage)
            ->subject('Contract Payment Approved: ' . $this->contract->contract_name)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your contract payment has been approved.')
            ->line('Contract Name: ' . $this->contract->contract_name)
            ->line('Contract ID: ' . $this->contract->unique_id)
            ->line('Amount: ' . number_format($this->contract->amount, 2))
            ->line('Payment Status: ' . $this->contract->payment_status)
            ->action('View Contract', $contractUrl)
            ->line('Thank you for using Yellow Penguin!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contract_id' => $this->contract->id,
            'contract_name' => $this->contract->contract_name,
            'customer_id' => $this->contract->customer_id,
            'amount' => $this->contract->amount,
            'payment_status' => $this->contract->payment_status,
        ];
    }
}
