<?php

namespace App\Notifications;

use App\Models\Contract;
use App\Models\SupportRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SupportRequestConvertedToContract extends Notification implements ShouldQueue
{
    use Queueable;

    protected $contract;
    protected $supportRequest;
    protected $frontendUrl;

    /**
     * Create a new notification instance.
     */
    public function __construct(Contract $contract, SupportRequest $supportRequest)
    {
        $this->contract = $contract;
        $this->supportRequest = $supportRequest;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $contractUrl = $this->frontendUrl . '/portal/contracts/' . $this->contract->id;

        return (new MailMessage)
            ->subject('Your Support Request Has Been Converted to a Contract')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your support request has been converted to a contract.')
            ->line('Support Request: ' . $this->supportRequest->title)
            ->line('Contract Name: ' . $this->contract->contract_name)
            ->line('Contract ID: ' . $this->contract->unique_id)
            ->line('Amount: ' . number_format($this->contract->amount, 2))
            ->line('Start Date: ' . ($this->contract->start_date ? $this->contract->start_date->format('Y-m-d') : 'Not specified'))
            ->action('View Contract', $contractUrl)
            ->line('Please review this contract at your earliest convenience.')
            ->line('Thank you for using Yellow Penguin!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contract_id' => $this->contract->id,
            'contract_name' => $this->contract->contract_name,
            'support_request_id' => $this->supportRequest->id,
            'amount' => $this->contract->amount,
        ];
    }
}
