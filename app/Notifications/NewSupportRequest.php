<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewSupportRequest extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    protected $supportRequest;
    protected $frontendUrl;

    public function __construct($supportRequest)
    {
        $this->supportRequest = $supportRequest;
        $this->frontendUrl = 'https://www.yellowpenguin.co.kr';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $userInfo = $this->supportRequest->user_id
            ? 'User: ' . $this->supportRequest->user->name . ' (' . $this->supportRequest->user->email . ')'
            : 'Guest: ' . $this->supportRequest->name . ' (' . $this->supportRequest->email . ')';

        $viewUrl = $this->frontendUrl . '/portal/support-requests/' . $this->supportRequest->id;

        return (new MailMessage)
                    ->subject('New Support Request: ' . $this->supportRequest->title)
                    ->greeting('Hello ' . $notifiable->name . ',')
                    ->line('A new support request has been submitted.')
                    ->line('Request Type: ' . $this->supportRequest->request_type)
                    ->line('Title: ' . $this->supportRequest->title)
                    ->line($userInfo)
                    ->action('View Request', $viewUrl)
                    ->line('Please respond to this request as soon as possible.')
                    ->line('Thank you for using Yellow Penguin!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
