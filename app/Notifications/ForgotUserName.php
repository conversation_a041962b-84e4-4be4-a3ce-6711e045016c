<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\URL;
use App\Models\UserOtp;
use App\Trait\HelperTrait;

class ForgotUserName extends Notification
{
    use Queueable, HelperTrait;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($user): MailMessage
    {
        return (new MailMessage)
            ->subject('Find Your User ID')
            ->greeting('Hello,')
            ->line('Your user ID is "'.$user->username.'"')
            ->line('Go to the login page and enter your User ID and Password.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}

