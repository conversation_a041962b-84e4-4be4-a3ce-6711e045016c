<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Support Request Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        input[type="number"],
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .development-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Test Support Request Form</h1>
    
    <form id="supportRequestForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="request_type">Request Type:</label>
            <input type="text" id="request_type" name="request_type" value="homepage" required>
        </div>
        
        <div class="form-group">
            <label for="title">Title:</label>
            <input type="text" id="title" name="title" value="Generate a Homepage" required>
        </div>
        
        <div class="form-group">
            <label for="description">Description:</label>
            <textarea id="description" name="description" rows="4" required><p>To know about details read the documents please</p></textarea>
        </div>
        
        <div class="form-group">
            <label for="base_construction_cost">Base Construction Cost:</label>
            <input type="number" id="base_construction_cost" name="base_construction_cost" value="330000">
        </div>
        
        <div class="form-group">
            <label for="monthly_subscription_fee">Monthly Subscription Fee:</label>
            <input type="number" id="monthly_subscription_fee" name="monthly_subscription_fee" value="55000">
        </div>
        
        <div class="form-group">
            <label for="additional_items_total">Additional Items Total:</label>
            <input type="number" id="additional_items_total" name="additional_items_total" value="750000">
        </div>
        
        <div class="form-group">
            <label for="total_construction_cost">Total Construction Cost:</label>
            <input type="number" id="total_construction_cost" name="total_construction_cost" value="1080000">
        </div>
        
        <h3>Development Items</h3>
        <div id="developmentItems">
            <div class="development-item">
                <div class="form-group">
                    <label>ID:</label>
                    <input type="number" name="development_items[0][id]" value="1">
                </div>
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="development_items[0][name]" value="Additional Page">
                </div>
                <div class="form-group">
                    <label>Quantity:</label>
                    <input type="number" name="development_items[0][quantity]" value="5">
                </div>
                <div class="form-group">
                    <label>Price:</label>
                    <input type="text" name="development_items[0][price]" value="100000.00">
                </div>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="number" name="development_items[0][total]" value="500000">
                </div>
            </div>
            
            <div class="development-item">
                <div class="form-group">
                    <label>ID:</label>
                    <input type="number" name="development_items[1][id]" value="4">
                </div>
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="development_items[1][name]" value="Bulletin Board">
                </div>
                <div class="form-group">
                    <label>Quantity:</label>
                    <input type="number" name="development_items[1][quantity]" value="1">
                </div>
                <div class="form-group">
                    <label>Price:</label>
                    <input type="text" name="development_items[1][price]" value="100000.00">
                </div>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="number" name="development_items[1][total]" value="100000">
                </div>
            </div>
            
            <div class="development-item">
                <div class="form-group">
                    <label>ID:</label>
                    <input type="number" name="development_items[2][id]" value="8">
                </div>
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="development_items[2][name]" value="Email Support">
                </div>
                <div class="form-group">
                    <label>Quantity:</label>
                    <input type="number" name="development_items[2][quantity]" value="1">
                </div>
                <div class="form-group">
                    <label>Price:</label>
                    <input type="text" name="development_items[2][price]" value="150000.00">
                </div>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="number" name="development_items[2][total]" value="150000">
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="files">Files:</label>
            <input type="file" id="files" name="files[]" multiple>
        </div>
        
        <button type="submit">Submit</button>
    </form>
    
    <div id="response" class="response">
        <h3>Response:</h3>
        <pre id="responseContent"></pre>
    </div>
    
    <script>
        document.getElementById('supportRequestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Convert development_items to JSON string
            const developmentItems = [];
            for (let i = 0; i < 3; i++) {
                developmentItems.push({
                    id: formData.get(`development_items[${i}][id]`),
                    name: formData.get(`development_items[${i}][name]`),
                    quantity: formData.get(`development_items[${i}][quantity]`),
                    price: formData.get(`development_items[${i}][price]`),
                    total: formData.get(`development_items[${i}][total]`)
                });
            }
            
            // Remove individual development_items fields
            for (let i = 0; i < 3; i++) {
                formData.delete(`development_items[${i}][id]`);
                formData.delete(`development_items[${i}][name]`);
                formData.delete(`development_items[${i}][quantity]`);
                formData.delete(`development_items[${i}][price]`);
                formData.delete(`development_items[${i}][total]`);
            }
            
            // Add development_items as JSON string
            formData.append('development_items', JSON.stringify(developmentItems));
            
            try {
                const response = await fetch('/api/support-requests', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                document.getElementById('responseContent').textContent = JSON.stringify(data, null, 2);
                document.getElementById('response').style.display = 'block';
            } catch (error) {
                document.getElementById('responseContent').textContent = 'Error: ' + error.message;
                document.getElementById('response').style.display = 'block';
            }
        });
    </script>
</body>
</html>
